# XTasker Product Requirements Document (PRD)

## Project Overview

XTasker is an AI-powered task management SaaS application that integrates ElizaOS as its core agent framework to provide intelligent task assistance and automation. Built on the Wasp.sh framework, XTasker combines the robust SaaS boilerplate features with advanced AI agent capabilities to create a comprehensive task management solution.

## Core Vision

Transform traditional task management by leveraging ElizaOS autonomous agents to provide:
- Intelligent task processing and categorization
- Natural language task creation and management
- AI-powered scheduling optimization
- Multi-agent collaboration for complex workflows
- Autonomous task assistance and completion suggestions

## Technical Architecture

### Current Foundation (Wasp.sh SaaS Boilerplate)
- **Frontend**: React 18 + TypeScript with Tailwind CSS and Shadcn/ui components
- **Backend**: Wasp framework with Prisma ORM and PostgreSQL
- **Authentication**: Email-based auth with JWT tokens
- **Payment Processing**: Stripe/Lemon Squeezy integration
- **File Management**: AWS S3 integration
- **Analytics**: Built-in dashboard and reporting

### ElizaOS Integration Requirements

#### 1. Agent Framework Integration
**Current State**: Basic LLM integration with custom providers
**Target State**: Full ElizaOS agent framework integration with shared database

**Implementation Requirements**:
- Integrate ElizaOS as a backend service alongside Wasp application
- Share PostgreSQL database between Wasp and ElizaOS using @elizaos/plugin-sql
- Maintain Wasp's authentication and user management system (no changes)
- Implement user-isolated agent environments within shared database
- Replace task-specific LLM operations with ElizaOS agent calls
- Integrate ElizaOS core packages (@elizaos/core, @elizaos/plugin-bootstrap)
- Implement agent persona management through ElizaOS character system
- Enable multi-agent workflows for complex task scenarios

**Database Sharing Strategy**:
- ElizaOS and Wasp share the same PostgreSQL database instance
- Wasp manages: Users, Tasks, Payments, Files, Analytics (existing schema)
- ElizaOS manages: Agents, Agent Memory, Agent Interactions (new schema)
- Cross-reference through userId to maintain data relationships
- Use database-level isolation to ensure user data privacy

#### 2. Agent Management Dashboard
**Current State**: Basic task interface (AppPage.tsx)
**Target State**: ElizaOS dashboard integration

**Implementation Requirements**:
- Replace current agent management with ElizaOS web interface
- Integrate ElizaOS dashboard at `/agents` route
- Provide agent creation, configuration, and monitoring capabilities
- Enable real-time agent status and performance tracking

#### 3. User Isolation and Security
**Current State**: Wasp handles all user authentication and authorization
**Target State**: Maintain Wasp auth while adding user-isolated agent environments

**Implementation Requirements**:
- **Preserve Wasp Authentication**: Keep existing JWT-based auth system unchanged
- **User Management Integrity**: Maintain all existing user management features
- **Agent Isolation**: Ensure each user's agents are completely isolated from others
- **Database-Level Security**: Implement Row Level Security (RLS) for agent data
- **API Security**: Secure agent endpoints with Wasp's existing auth middleware

#### 4. Strong Typing Enforcement
**Current State**: Partial TypeScript configuration
**Target State**: Zero 'any' types allowed

**Implementation Requirements**:
- Enhanced TypeScript compiler options (already partially implemented)
- ESLint rules to prevent 'any' type usage
- Comprehensive type definitions for all ElizaOS integrations
- Type-safe agent configuration and communication

## Feature Specifications

### Phase 1: ElizaOS Core Integration
1. **Agent Framework Setup**
   - Install and configure ElizaOS packages
   - Replace existing LLM operations with ElizaOS agents
   - Implement agent lifecycle management
   - Configure agent communication protocols

2. **Database Schema Updates**
   - Add ElizaOS agent entities to Prisma schema
   - Implement agent configuration storage
   - Add agent interaction history tracking
   - Maintain backward compatibility with existing Task entities

3. **Strong Typing Implementation**
   - Configure TypeScript strict mode settings
   - Add ESLint rules: `@typescript-eslint/no-explicit-any: error`
   - Implement comprehensive type definitions
   - Ensure all ElizaOS integrations are strongly typed

### Phase 2: Agent Management Interface
1. **ElizaOS Dashboard Integration**
   - Embed ElizaOS web interface at `/agents` route
   - Implement agent creation and configuration UI
   - Add agent performance monitoring
   - Integrate with existing Wasp authentication

2. **Agent-Task Integration**
   - Connect ElizaOS agents to task processing pipeline
   - Implement natural language task creation via agents
   - Add agent-powered task categorization and prioritization
   - Enable agent-suggested task dependencies

### Phase 3: Advanced Agent Features
1. **Multi-Agent Workflows**
   - Implement agent collaboration for complex tasks
   - Add agent specialization (scheduling, research, execution)
   - Enable agent-to-agent communication
   - Implement swarm intelligence for task optimization

2. **Autonomous Task Management**
   - Agent-powered automatic task breakdown
   - Intelligent scheduling based on user patterns
   - Proactive task suggestions and reminders
   - Context-aware task prioritization

## Technical Implementation Plan

### 1. Package Dependencies
```json
{
  "dependencies": {
    "@elizaos/core": "latest",
    "@elizaos/plugin-bootstrap": "latest",
    "@elizaos/plugin-sql": "latest",
    "@elizaos/cli": "latest"
  }
}
```

### 2. Shared Database Configuration
```typescript
// ElizaOS database configuration to use existing Wasp PostgreSQL
const elizaDbConfig = {
  connectionString: process.env.DATABASE_URL, // Same as Wasp
  schema: "eliza", // Separate schema for ElizaOS tables
  userIsolation: true, // Enable user-based data isolation
  rowLevelSecurity: true // Enable RLS for multi-tenant security
};

// Wasp continues to use its existing database configuration
// No changes needed to existing Wasp database setup
```

### 3. TypeScript Configuration Updates
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noImplicitOverride": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true,
    "noPropertyAccessFromIndexSignature": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true
  }
}
```

### 4. ESLint Configuration
```json
{
  "rules": {
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/no-unsafe-assignment": "error",
    "@typescript-eslint/no-unsafe-call": "error",
    "@typescript-eslint/no-unsafe-member-access": "error",
    "@typescript-eslint/no-unsafe-return": "error"
  }
}
```

### 5. Database Schema Extensions (Shared Database Approach)
```prisma
// Wasp Schema (Existing - No Changes)
// User, Task, File, DailyStats, LlmConfiguration, LlmResponse models remain unchanged

// New ElizaOS Integration Schema (Added to existing Prisma schema)
model Agent {
  id                String          @id @default(uuid())
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  // Link to existing Wasp User model - maintains auth integrity
  user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId            String

  name              String
  description       String?
  characterConfig   String          // JSON string for ElizaOS character
  isActive          Boolean         @default(true)

  // ElizaOS specific fields
  elizaAgentId      String?         // Reference to ElizaOS internal agent ID
  memoryNamespace   String          // Isolated memory space per user

  agentInteractions AgentInteraction[]
  agentMemories     AgentMemory[]

  @@unique([userId, name])
  @@index([userId]) // Optimize user-based queries
}

model AgentInteraction {
  id                String          @id @default(uuid())
  createdAt         DateTime        @default(now())

  agent             Agent           @relation(fields: [agentId], references: [id], onDelete: Cascade)
  agentId           String

  // Optional link to existing Wasp Task model
  task              Task?           @relation(fields: [taskId], references: [id], onDelete: SetNull)
  taskId            String?

  interactionType   String          // 'task_creation', 'task_update', 'suggestion', etc.
  content           String
  metadata          String?         // JSON string for additional data

  @@index([agentId])
}

model AgentMemory {
  id                String          @id @default(uuid())
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt

  agent             Agent           @relation(fields: [agentId], references: [id], onDelete: Cascade)
  agentId           String

  // ElizaOS memory fields
  memoryType        String          // 'conversation', 'fact', 'goal', etc.
  content           String
  importance        Float           @default(0.5)
  embedding         String?         // Vector embedding for similarity search

  @@index([agentId, memoryType])
  @@index([agentId, importance])
}

// Row Level Security (RLS) Policies (PostgreSQL)
// These ensure user isolation at the database level:
//
// CREATE POLICY agent_user_isolation ON "Agent"
//   FOR ALL USING ("userId" = current_setting('app.current_user_id')::uuid);
//
// CREATE POLICY agent_interaction_user_isolation ON "AgentInteraction"
//   FOR ALL USING (EXISTS (
//     SELECT 1 FROM "Agent" WHERE "Agent"."id" = "AgentInteraction"."agentId"
//     AND "Agent"."userId" = current_setting('app.current_user_id')::uuid
//   ));
//
// CREATE POLICY agent_memory_user_isolation ON "AgentMemory"
//   FOR ALL USING (EXISTS (
//     SELECT 1 FROM "Agent" WHERE "Agent"."id" = "AgentMemory"."agentId"
//     AND "Agent"."userId" = current_setting('app.current_user_id')::uuid
//   ));
```

## Integration Points

### 1. Wasp.sh Framework Compatibility
- **Complete Preservation**: All existing Wasp features remain 100% unchanged
- **Authentication Integrity**: Wasp's JWT-based auth system handles all user management
- **Payment Processing**: Stripe/Lemon Squeezy integration remains intact
- **File Upload**: AWS S3 integration continues to work as before
- **Admin Dashboard**: Existing analytics and admin features preserved
- **Database Sharing**: ElizaOS uses same PostgreSQL instance with separate schema/namespace

### 2. User Isolation and Security Architecture
- **Database-Level Isolation**: Row Level Security (RLS) ensures users only access their agents
- **Memory Namespace Isolation**: Each user's agents have isolated memory spaces
- **API Security**: All agent endpoints protected by existing Wasp auth middleware
- **Agent Ownership**: Agents are strictly tied to user accounts via foreign keys
- **Cross-User Prevention**: Database policies prevent any cross-user data access

### 3. ElizaOS Agent Communication
- Implement agent message handling through Wasp operations
- Use ElizaOS plugin system for task-specific capabilities
- Integrate agent memory and context management
- Enable real-time agent-user communication

### 4. UI/UX Integration
- Embed ElizaOS dashboard within existing UI framework
- Maintain Twitter-like interface design consistency
- Implement responsive design for agent management
- Ensure seamless navigation between task and agent interfaces

## Success Criteria

### Technical Metrics
- Zero 'any' types in codebase (enforced by TypeScript and ESLint)
- All ElizaOS integrations fully typed and tested
- Agent response time < 2 seconds for basic operations
- 99.9% uptime for agent services

### User Experience Metrics
- Seamless agent creation and configuration process
- Intuitive agent-task interaction workflows
- Real-time agent status visibility
- Comprehensive agent performance analytics

### Business Metrics
- Improved task completion rates through AI assistance
- Reduced time-to-task-completion with agent help
- Increased user engagement with intelligent features
- Scalable multi-agent architecture for enterprise use

## Risk Mitigation

### Technical Risks
- **ElizaOS Version Compatibility**: Pin to stable ElizaOS versions, implement gradual updates
- **Type Safety Enforcement**: Implement incremental typing improvements, comprehensive testing
- **Performance Impact**: Monitor agent response times, implement caching strategies

### Integration Risks
- **Wasp Framework Conflicts**: Maintain clear separation of concerns, thorough testing
- **Database Migration**: Implement backward-compatible schema changes
- **UI Consistency**: Establish design system guidelines for ElizaOS components

## Development Approach

Following the modified vertical slice implementation approach for LLM-assisted coding:

1. **Start with Basic Implementation**: Core ElizaOS integration with minimal features
2. **Iterative Enhancement**: Add complexity incrementally with each development cycle
3. **Feature-Complete Slices**: Each phase delivers working, testable functionality
4. **Continuous Integration**: Maintain working application throughout development
5. **User Feedback Integration**: Incorporate user testing at each phase

This PRD serves as the foundation for implementing XTasker with ElizaOS integration while maintaining the robust SaaS boilerplate features and ensuring strong typing throughout the application.

## Implementation Roadmap

### Phase 1: Foundation (Weeks 1-2)
**Goal**: Establish ElizaOS integration foundation with strong typing

**Deliverables**:
- ElizaOS packages installed and configured
- TypeScript strict mode fully enforced
- ESLint rules preventing 'any' types
- Basic agent entity in database schema
- Core agent operations (create, read, update, delete)

**Success Criteria**:
- Zero TypeScript 'any' types in codebase
- ElizaOS core functionality operational
- All existing Wasp features remain functional

### Phase 2: Agent Management (Weeks 3-4)
**Goal**: Implement comprehensive agent management interface

**Deliverables**:
- ElizaOS dashboard integrated at `/agents` route
- Agent creation and configuration UI
- Agent status monitoring and performance metrics
- Integration with existing Wasp authentication system

**Success Criteria**:
- Users can create and manage agents through UI
- Real-time agent status visibility
- Seamless integration with existing application flow

### Phase 3: Task-Agent Integration (Weeks 5-6)
**Goal**: Connect agents to task management workflows

**Deliverables**:
- Agent-powered task creation from natural language
- Intelligent task categorization and prioritization
- Agent suggestions for task dependencies
- Enhanced task processing pipeline

**Success Criteria**:
- Agents can process and create tasks from user input
- Improved task organization through AI assistance
- Measurable improvement in task completion efficiency

### Phase 4: Advanced Features (Weeks 7-8)
**Goal**: Implement multi-agent workflows and autonomous features

**Deliverables**:
- Multi-agent collaboration system
- Agent specialization (scheduling, research, execution)
- Autonomous task breakdown and optimization
- Proactive task suggestions and reminders

**Success Criteria**:
- Multiple agents can collaborate on complex tasks
- Autonomous task management reduces user workload
- Advanced AI features provide significant value to users

## Technical Considerations

### ElizaOS Architecture Integration
- **Agent Runtime**: Integrate ElizaOS agent runtime with Wasp server
- **Character System**: Implement ElizaOS character configuration for agent personas
- **Plugin System**: Utilize ElizaOS plugins for task-specific capabilities
- **Memory Management**: Leverage ElizaOS memory system for context retention

### Performance Optimization
- **Agent Response Caching**: Implement caching for frequent agent operations
- **Database Optimization**: Optimize queries for agent-task relationships
- **Real-time Updates**: Use WebSocket connections for live agent status
- **Resource Management**: Monitor and limit agent resource consumption

### Security and Privacy
- **Agent Access Control**: Implement user-specific agent permissions
- **Data Encryption**: Encrypt sensitive agent configuration data
- **API Security**: Secure agent communication endpoints
- **Privacy Compliance**: Ensure agent data handling meets privacy standards

This comprehensive PRD provides the foundation for transforming XTasker into a cutting-edge AI-powered task management platform while maintaining the robust SaaS infrastructure and ensuring enterprise-grade code quality through strong typing enforcement.
```


# XTasker - Features and Application Structure

## Application Overview
XTasker is a task management SaaS application built with Wasp framework, integrating ElizaOS agent framework for AI-powered task assistance and automation. The application follows Wasp.sh conventions and boilerplate structure while providing a Twitter-like interface design similar to the main task3 repository.

## Core Features Implementation

### 1. AI-Powered Task Management
**How it works:**
- Uses custom LLM providers with configurable base URLs, models, and parameters
- ElizaOS agents process natural language input and convert to actionable tasks
- AI analyzes task complexity and suggests time estimates and priorities
- Automatic task categorization and dependency detection
- Support for multiple LLM providers (OpenAI, Anthropic, local models, etc.)

**Components:**
- `AppPage.tsx` - Main task interface with AI scheduler (renamed from DemoAppPage)
- `operations.ts` - Backend operations for LLM integration
- `schedule.ts` - Task scheduling logic and data structures
- `LlmSettings.tsx` - Custom LLM configuration interface

### 2. User Authentication System (Wasp Boilerplate)
**How it works:**
- Email-based authentication with verification (Wasp built-in)
- Password reset functionality via email
- JWT token management through Wasp auth system
- Role-based access control (user/admin)
- Maintains Wasp.sh auth conventions and structure

**Components:**
- `AuthPageLayout.tsx` - Authentication wrapper component
- `LoginPage.tsx` - User login interface
- `SignupPage.tsx` - User registration interface
- `userSignupFields.ts` - User registration field configuration
- Email templates in `auth/email-and-pass/emails.ts`

### 3. File Management System (Wasp Boilerplate)
**How it works:**
- AWS S3 integration for secure file storage
- Pre-signed URLs for direct browser uploads
- File validation and type checking
- User-specific file organization
- Follows Wasp file upload patterns

**Components:**
- `FileUploadPage.tsx` - File upload interface with progress tracking
- `fileUploading.ts` - Upload logic and validation
- `s3Utils.ts` - AWS S3 integration utilities
- `operations.ts` - Backend file operations

### 4. Payment and Subscription Management (Wasp Boilerplate)
**How it works:**
- Multiple payment processors (Stripe, Lemon Squeezy)
- Subscription plans with different feature tiers
- Credit-based usage tracking
- Customer portal for self-service management
- Maintains Wasp payment structure

**Components:**
- `PricingPage.tsx` - Subscription plans display
- `CheckoutPage.tsx` - Payment processing interface
- `plans.ts` - Payment plan configurations
- `paymentProcessor.ts` - Payment logic abstraction
- Webhook handlers for payment events

### 5. Analytics and Reporting (Wasp Boilerplate)
**How it works:**
- Daily statistics collection and aggregation
- Page view tracking and source attribution
- Revenue and user growth metrics
- Real-time dashboard updates
- Uses Wasp job scheduling for stats

**Components:**
- `AnalyticsDashboardPage.tsx` - Main analytics interface
- `TotalPageViewsCard.tsx` - Page view metrics display
- `TotalRevenueCard.tsx` - Revenue tracking
- `RevenueAndProfitChart.tsx` - Visual analytics
- `stats.ts` - Analytics data processing

### 6. Admin Management System (Wasp Boilerplate)
**How it works:**
- Role-based admin access control
- User management and monitoring
- System health and performance monitoring
- Content and configuration management
- Follows Wasp admin patterns

**Components:**
- `DefaultLayout.tsx` - Admin dashboard layout
- `UsersDashboardPage.tsx` - User management interface
- `SettingsPage.tsx` - System configuration
- `useRedirectHomeUnlessUserIsAdmin.ts` - Admin access control

## UI Design Structure (Similar to Task3 Main Repo)

### Twitter-like Interface Components
**Main Layout:**
- `Header.tsx` - Top navigation with user profile and notifications
- `Sidebar.tsx` - Left navigation panel with main menu items
- `MobileSidebar.tsx` - Mobile-responsive navigation
- `MobileBottomNav.tsx` - Bottom navigation for mobile

**Task Management Interface:**
- `TweetComposer.tsx` - Task creation interface (Twitter-like composer)
- `AgentList.tsx` - List of available AI agents
- `AgentForm.tsx` - Agent configuration and settings
- `SchedulingView.tsx` - Calendar and scheduling interface
- `TweetCalendar.tsx` - Task timeline view

**Dashboard Components:**
- `DashboardView.tsx` - Main dashboard with task overview
- `StatsCard.tsx` - Metrics and statistics cards
- `MediaLibraryView.tsx` - File and media management
- `NotificationDropdown.tsx` - Real-time notifications

**Mobile-First Design:**
- `MobileDashboard.tsx` - Mobile dashboard layout
- `MobileHeader.tsx` - Mobile navigation header
- `MobileTweetComposer.tsx` - Mobile task creation
- Responsive design patterns throughout

## Application Structure (Respecting Wasp.sh Conventions)

### Frontend Architecture (Wasp Client Structure)
```
src/
├── components/ui/          # Reusable UI components (Shadcn/ui)
├── client/                 # Client-side application logic (Wasp structure)
├── auth/                   # Authentication components (Wasp auth)
├── app/                    # Main app features (renamed from demo-ai-app)
├── file-upload/           # File management system (Wasp boilerplate)
├── payment/               # Payment and subscription logic (Wasp boilerplate)
├── user/                  # User profile and account management (Wasp)
├── admin/                 # Administrative interfaces (Wasp boilerplate)
├── landing-page/          # Marketing and landing pages (Wasp)
├── analytics/             # Analytics and reporting (Wasp boilerplate)
└── shared/                # Shared utilities and constants (Wasp)
```

### Backend Architecture (Wasp Server Structure)
```
server/
├── operations/            # Wasp operations (queries/actions)
├── scripts/               # Database seeds and utilities
├── validation/            # Input validation schemas
└── utils/                 # Server-side utilities
```

### Database Schema (Updated for XTasker)
```
User (Wasp boilerplate)
├── id, email, username
├── subscription data (status, plan, credits)
├── payment processor integration
└── relationships: tasks, files, llmResponses

Task (Enhanced)
├── id, description, time, isDone
├── user relationship
├── AI-enhanced metadata
├── priority, category, dependencies
└── ElizaOS agent interactions

File (Wasp boilerplate)
├── id, name, type, key, uploadUrl
└── user relationship

LlmResponse (Renamed from GptResponse)
├── id, content, createdAt
├── model, provider, parameters
├── user relationship
└── custom LLM configuration

DailyStats (Wasp boilerplate)
├── analytics metrics
├── revenue tracking
└── page view sources

LlmConfiguration (New)
├── id, name, provider
├── baseUrl, model, parameters
├── user relationship
└── custom settings
```

## Custom LLM Integration

### LLM Provider Support
**Supported Providers:**
- OpenAI (GPT-3.5, GPT-4, custom deployments)
- Anthropic (Claude models)
- Local models (Ollama, LM Studio)
- Custom API endpoints
- Azure OpenAI Service
- Google AI (Gemini)

**Configuration Options:**
- Custom base URLs for API endpoints
- Model selection and parameters
- Temperature, max tokens, top-p settings
- Custom headers and authentication
- Rate limiting and retry logic

**Components:**
- `LlmSettings.tsx` - LLM provider configuration
- `ModelSelector.tsx` - Model selection interface
- `CustomEndpointForm.tsx` - Custom API configuration
- `llmConfig.ts` - LLM configuration management

## Component Integration Flow (Wasp-Compliant)

### 1. Task Creation Flow
1. User inputs task via `TweetComposer.tsx` interface
2. ElizaOS agent processes natural language input
3. `generateLlmResponse` operation calls configured LLM provider
4. Structured task data returned and stored
5. UI updates with new task and AI suggestions

### 2. File Upload Flow (Wasp Boilerplate)
1. User selects file in `FileUploadPage.tsx`
2. File validation occurs client-side
3. `createFile` operation generates S3 signed URL
4. Direct upload to S3 with progress tracking
5. File metadata stored in database

### 3. Payment Flow (Wasp Boilerplate)
1. User selects plan on `PricingPage.tsx`
2. `generateCheckoutSession` creates payment session
3. Redirect to payment processor
4. Webhook processes payment completion
5. User subscription status updated

### 4. Admin Analytics Flow (Wasp Boilerplate)
1. Daily stats job aggregates data
2. `AnalyticsDashboardPage.tsx` queries metrics
3. Real-time charts and cards display data
4. Admin can drill down into specific metrics

## ElizaOS Agent Integration Points

### 1. Task Processing
- Natural language task input parsing
- Task complexity analysis and estimation
- Automatic categorization and tagging
- Dependency detection and suggestion

### 2. Schedule Optimization
- AI-powered daily schedule generation
- Priority-based task ordering
- Time block optimization
- Conflict resolution and suggestions

### 3. User Assistance
- Contextual help and guidance
- Task completion suggestions
- Productivity insights and recommendations
- Automated follow-up and reminders

## Technology Stack Integration (Wasp-Based)

### Frontend (Wasp Client)
- **React 18** with TypeScript for type safety
- **Tailwind CSS** for styling with **Shadcn/ui** components
- **Wasp client** for API integration and routing
- **Lucide React** for consistent iconography
- **Twitter-like UI patterns** from task3 repo

### Backend (Wasp Server)
- **Wasp framework** for full-stack coordination
- **Prisma ORM** for type-safe database operations
- **PostgreSQL** for data persistence
- **ElizaOS** for AI agent capabilities
- **PgBoss** for job scheduling (Wasp built-in)

### External Services
- **Custom LLM APIs** with configurable endpoints
- **AWS S3** for file storage (Wasp boilerplate)
- **Stripe/Lemon Squeezy** for payment processing (Wasp boilerplate)
- **Email services** for notifications (Wasp boilerplate)

## Development and Deployment (Wasp Workflow)

### Development Setup
- Wasp development server with hot reload
- PostgreSQL database with Prisma migrations
- Environment configuration for API keys and LLM endpoints
- Local S3 simulation for file testing

### Production Deployment
- Wasp build and deployment pipeline
- Database migration management
- Environment variable configuration
- CDN integration for static assets

## Key Wasp.sh Conventions Maintained

### 1. File Structure
- Maintains Wasp's src/ directory structure
- Preserves Wasp's operation patterns (queries/actions)
- Keeps Wasp's entity relationships intact
- Follows Wasp's page and route definitions

### 2. Authentication
- Uses Wasp's built-in auth system
- Maintains email verification flow
- Preserves admin role checking patterns
- Keeps Wasp's user entity structure

### 3. Database
- Uses Wasp's Prisma integration
- Maintains existing entity relationships
- Preserves Wasp's migration patterns
- Keeps Wasp's seeding structure

### 4. Operations
- Follows Wasp's query/action patterns
- Maintains Wasp's entity access control
- Preserves Wasp's validation patterns
- Keeps Wasp's error handling conventions