name: ci

# Cancel previous runs for the same PR/branch
concurrency:
  group: ci-${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
jobs:
  # Test job
  test:
    # Skip duplicate runs: run on push to main, or on pull_request events only
    if: github.event_name == 'pull_request' || (github.event_name == 'push' && github.ref_name == 'main')
    runs-on: ubuntu-latest
    timeout-minutes: 20
    env:
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ vars.TURBO_TEAM }}
      TURBO_REMOTE_ONLY: true
      PGLITE_WASM_MODE: node
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v2
      - uses: actions/setup-node@v4
        with:
          node-version: '23'

      - name: Install dependencies
        run: bun install

      - name: Create test env file
        run: |
          echo "TEST_DATABASE_CLIENT=pglite" > packages/core/.env.test
          echo "NODE_ENV=test" >> packages/core/.env.test

      - name: Run tests
        timeout-minutes: 15
        env:
          NODE_OPTIONS: '--max-old-space-size=2048'
        run: cd packages/core && bun test:coverage --timeout 60000

  # Lint and format job
  lint-and-format:
    # Skip duplicate runs: run on push to main, or on pull_request events only
    if: github.event_name == 'pull_request' || (github.event_name == 'push' && github.ref_name == 'main')
    runs-on: ubuntu-latest
    timeout-minutes: 5
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ vars.TURBO_TEAM }}
      TURBO_REMOTE_ONLY: true
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v2
      - uses: actions/setup-node@v4
        with:
          node-version: '23'

      - name: Install dependencies
        run: bun install

      - name: Check format
        run: bun run format:check

      - name: Run lint
        run: bun run lint

  # Build job
  build:
    # Skip duplicate runs: run on push to main, or on pull_request events only
    if: github.event_name == 'pull_request' || (github.event_name == 'push' && github.ref_name == 'main')
    runs-on: ubuntu-latest
    timeout-minutes: 8
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ vars.TURBO_TEAM }}
      TURBO_REMOTE_ONLY: true
    steps:
      - uses: actions/checkout@v4
      - uses: oven-sh/setup-bun@v2
      - uses: actions/setup-node@v4
        with:
          node-version: '23'

      - name: Install dependencies
        run: bun install

      - name: Build packages
        run: bun run build && bun run build:docs
