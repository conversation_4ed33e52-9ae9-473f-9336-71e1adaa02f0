name: Client Cypress Tests

# Cancel previous runs for the same PR/branch
concurrency:
  group: client-cypress-${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  GH_TOKEN: ${{ secrets.GH_TOKEN }}
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
  ELIZA_NONINTERACTIVE: true
  # Skip unnecessary downloads during CI
  PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1

on:
  push:
    branches:
      - 'main'
      - 'develop'
    paths:
      - 'packages/client/**'
      - 'packages/core/**'
      - 'packages/server/**'
      - '.github/workflows/client-cypress-tests.yml'
  pull_request:
    branches:
      - 'main'
      - 'develop'
    paths:
      - 'packages/client/**'
      - 'packages/core/**'
      - 'packages/server/**'
      - '.github/workflows/client-cypress-tests.yml'

jobs:
  # Component tests (no server needed)
  cypress-component:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 23

      - name: Set up Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Build packages (Turbo cached)
        run: bun run build

      - name: Install Cypress binary
        working-directory: packages/client
        run: bunx cypress install

      - name: Run Cypress Component Tests
        working-directory: packages/client
        run: |
          bun run test:component
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # E2E tests (needs server)
  cypress-e2e:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        # Can add parallelization here: containers: [1, 2, 3]
        containers: [1]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 23

      - name: Set up Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Build packages (Turbo cached)
        run: bun run build

      - name: Install Cypress binary
        working-directory: packages/client
        run: bunx cypress install

      - name: Create .env file
        run: |
          echo "OPENAI_API_KEY=$OPENAI_API_KEY" > .env
          echo "LOG_LEVEL=info" >> .env

      - name: Run E2E tests with server
        working-directory: packages/client
        run: |
          chmod +x scripts/test-e2e-with-server.sh
          ./scripts/test-e2e-with-server.sh
        env:
          SERVER_PORT: 3000
          CLIENT_PORT: 5173

  # Summary job
  cypress-tests-summary:
    needs: [cypress-component, cypress-e2e]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Check test results
        run: |
          component_result="${{ needs.cypress-component.result }}"
          e2e_result="${{ needs.cypress-e2e.result }}"

          if [[ "$component_result" == "failure" || "$e2e_result" == "failure" ]]; then
            echo "❌ Cypress tests failed"
            echo "Component tests: $component_result"
            echo "E2E tests: $e2e_result"
            exit 1
          elif [[ "$component_result" == "cancelled" || "$e2e_result" == "cancelled" ]]; then
            echo "⏹️ Cypress tests were cancelled"
            exit 1
          else
            echo "✅ All Cypress tests passed"
            echo "Component tests: $component_result"
            echo "E2E tests: $e2e_result"
          fi
