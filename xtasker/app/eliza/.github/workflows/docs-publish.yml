name: Deploy Documentation

on:
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:

jobs:
  deploy-docs:
    name: Deploy Documentation
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '23'

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Install Dependencies
        working-directory: packages/docs
        run: bun install

      - name: Build Documentation
        working-directory: packages/docs
        run: bun run build

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./packages/docs/build
          user_name: 'github-actions[bot]'
          user_email: '41898282+github-actions[bot]@users.noreply.github.com'
          publish_branch: gh-pages
