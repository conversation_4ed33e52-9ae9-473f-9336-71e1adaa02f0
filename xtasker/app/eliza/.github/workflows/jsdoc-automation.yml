name: Autodocs Documentation Generator

on:
  workflow_dispatch:
    inputs:
      jsdoc:
        description: 'Generate code comments (T/F)'
        required: true
        default: 'T'
        type: string
        options: [T, F]
      readme:
        description: 'Generate README documentation (T/F)'
        required: true
        default: 'T'
        type: string
        options: [T, F]
      pull_number:
        description: 'Pull Request Number (if not provided, scans root_directory) - PR must be merged to develop branch. DONT provide if `README documentation` is T from above'
        required: false
        type: string
      root_directory:
        description: 'Only scans files in this directory (relative to repository root, e.g., packages/core/src)'
        required: true
        default: 'packages/core'
        type: string
      excluded_directories:
        description: 'Directories to exclude from scanning (comma-separated, relative to root_directory)'
        required: true
        default: 'node_modules,dist,test'
        type: string
      reviewers:
        description: 'Pull Request Reviewers (Must be collaborator on the repository) comma-separated GitHub usernames'
        required: true
        default: ''
        type: string
      branch:
        description: 'Target branch for PR (defaults to develop)'
        required: false
        default: 'develop'
        type: string

jobs:
  generate-docs:
    runs-on: ubuntu-latest

    permissions:
      contents: write
      pull-requests: write

    env:
      GITHUB_ACCESS_TOKEN: ${{ secrets.GH_PAT }}
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Initialize submodules manually
        run: |
          # Initialize submodules with proper error handling
          if git submodule status | grep -q '^-'; then
            echo "Initializing submodules..."
            git submodule update --init --recursive || echo "Submodule initialization failed, continuing..."
          else
            echo "Submodules already initialized or not present"
          fi

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '23.3.0'

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install system dependencies
        run: |
          sudo apt-get update -y
          sudo apt-get install -y --no-install-recommends \
            git \
            make \
            build-essential \
            unzip \
            curl

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: '1.2.15'

      - name: Verify installations
        run: |
          node -v
          python3 --version
          bun --version

      - name: Install root dependencies
        run: |
          # Skip postinstall script to avoid submodule issues in CI
          SKIP_POSTINSTALL=1 bun install
        env:
          SKIP_POSTINSTALL: 1

      - name: Install package dependencies
        working-directory: packages/autodoc
        run: bun install

      - name: Build TypeScript
        working-directory: packages/autodoc
        run: bun run build

      - name: Run documentation generator
        working-directory: packages/autodoc
        run: bun run autodoc
        env:
          INPUT_ROOT_DIRECTORY: ${{ github.event.inputs.root_directory }}
          INPUT_PULL_NUMBER: ${{ github.event.inputs.pull_number }}
          INPUT_EXCLUDED_DIRECTORIES: ${{ github.event.inputs.excluded_directories }}
          INPUT_REVIEWERS: ${{ github.event.inputs.reviewers }}
          INPUT_BRANCH: ${{ github.event.inputs.branch }}
          INPUT_JSDOC: ${{ github.event.inputs.jsdoc }}
          INPUT_README: ${{ github.event.inputs.readme }}
