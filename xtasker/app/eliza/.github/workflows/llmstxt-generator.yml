name: Repomix Documentation Generator

on:
  workflow_dispatch:
    inputs:
      target_branch:
        description: 'Target branch for changes (defaults to autodocs)'
        required: false
        default: 'autodocs'
        type: string
      create_pr:
        description: 'Create a pull request instead of pushing directly (T/F)'
        required: false
        default: 'F'
        type: string
      base_branch:
        description: 'Base branch for PR (if creating PR)'
        required: false
        default: 'main'
        type: string

jobs:
  generate-docs:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '23'

      - name: Install Repomix globally
        run: npm install -g repomix

      - name: Run Repomix with standard config
        run: repomix -c scripts/repomix.config.json

      - name: Run Repomix with full config
        run: repomix -c scripts/repomix-full.config.json

      - name: Set up Git user
        run: |
          git config --global user.name "GitHub Action"
          git config --global user.email "<EMAIL>"

      - name: Check if target branch exists
        id: check_branch
        run: |
          if git ls-remote --heads origin ${{ inputs.target_branch }} | grep -q ${{ inputs.target_branch }}; then
            echo "branch_exists=true" >> $GITHUB_OUTPUT
          else
            echo "branch_exists=false" >> $GITHUB_OUTPUT
          fi

      - name: Create target branch if it doesn't exist
        if: steps.check_branch.outputs.branch_exists == 'false'
        run: git checkout -b ${{ inputs.target_branch }}

      - name: Switch to target branch if it exists
        if: steps.check_branch.outputs.branch_exists == 'true'
        run: |
          git fetch origin ${{ inputs.target_branch }}:${{ inputs.target_branch }}
          git checkout ${{ inputs.target_branch }}

      - name: Commit changes
        id: commit
        run: |
          git add packages/docs/static/llms.txt packages/docs/static/llms-full.txt
          if git diff --staged --quiet; then
            echo "No changes to commit"
            echo "changes_made=false" >> $GITHUB_OUTPUT
          else
            git commit -m "Update documentation with Repomix [skip ci]"
            echo "changes_made=true" >> $GITHUB_OUTPUT
          fi

      - name: Push changes directly to branch
        if: inputs.create_pr == 'F' && steps.commit.outputs.changes_made == 'true'
        run: git push origin ${{ inputs.target_branch }}

      - name: Create Pull Request
        if: inputs.create_pr == 'T' && steps.commit.outputs.changes_made == 'true'
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'Update documentation with Repomix [skip ci]'
          title: 'Update documentation with Repomix'
          body: |
            This PR was automatically generated by the Repomix Documentation Generator workflow.

            It updates the documentation files:
            - packages/docs/static/llms.txt
            - packages/docs/static/llms-full.txt
          branch: ${{ inputs.target_branch }}
          base: ${{ inputs.base_branch }}
          draft: false
