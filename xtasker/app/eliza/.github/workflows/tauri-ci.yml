name: Native App

env:
  GH_TOKEN: ${{ secrets.GH_TOKEN }}
  GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
  # Skip binary downloads during install
  YOUTUBE_DL_SKIP_DOWNLOAD: true
  ADBLOCK: true
  PUPPETEER_SKIP_DOWNLOAD: true
  CYPRESS_INSTALL_BINARY: 0
  PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1

on:
  push:
    branches: [main, develop]
    paths:
      - 'packages/app/**'
      - '.github/workflows/**'

jobs:
  # Platform-specific builds run in parallel
  test-build:
    strategy:
      fail-fast: false
      matrix:
        platform: [ubuntu-latest, macos-latest, windows-latest]

    runs-on: ${{ matrix.platform }}
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ secrets.TURBO_TEAM }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '23'

      - name: Install Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Verify Bun installation
        run: |
          bun --version
          which bun
          file $(which bun) || true
        shell: bash

      - name: Setup Rust
        run: |
          rustup update stable
          rustup default stable
        shell: bash

      - name: Install dependencies (ubuntu only)
        if: matrix.platform == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libgtk-3-dev libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf libssl-dev libx11-dev libxdo-dev libxcb1-dev libxcb-render0-dev libxcb-shape0-dev libxcb-xfixes0-dev

      - name: Install Windows dependencies
        if: matrix.platform == 'windows-latest'
        run: |
          # Install WebView2 Bootstrapper (Edge Chromium)
          Invoke-WebRequest -Uri https://go.microsoft.com/fwlink/p/?LinkId=2124703 -OutFile ./MicrosoftEdgeWebview2Setup.exe
          Start-Process -FilePath ./MicrosoftEdgeWebview2Setup.exe -ArgumentList "/silent", "/install" -Wait
          # Add any other Windows-specific dependencies here if known (e.g., C++ Build Tools if not pre-installed on runners)
          # For now, focus on WebView2. We might need to install Visual Studio Build Tools
          #choco install visualstudio2019buildtools --package-parameters "--add Microsoft.VisualStudio.Component.VC.Tools.x86.x64 --add Microsoft.VisualStudio.Component.Windows10SDK" -y
          # echo "Chocolatey and VS Build tools setup might be needed if not available"
        shell: pwsh

      - name: Install dependencies (platform-specific)
        run: bun install
        env:
          NPM_CONFIG_IGNORE_SCRIPTS: true

      - name: Build packages
        run: bun run build

      - name: Run TypeScript check
        run: |
          cd packages/app
          # Verify bun is working before running typecheck
          bun --version
          bun run typecheck
        shell: bash

      - name: Install Tauri CLI
        run: |
          bun install -g @tauri-apps/cli
        shell: bash

      - name: Test build (verify it compiles)
        run: |
          cd packages/app
          # Ensure bun is in PATH and working
          export PATH="$HOME/.bun/bin:$PATH"
          bun --version
          bun run tauri build --debug
        shell: bash
