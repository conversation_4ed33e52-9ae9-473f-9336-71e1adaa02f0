name: Release Native App

on:
  release:
    types: [created, edited, published, prereleased]
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version number (e.g., 1.0.0)'
        required: false
        default: ''
      draft:
        description: 'Create as draft release'
        type: boolean
        default: true
      prerelease:
        description: 'Mark as prerelease'
        type: boolean
        default: false

env:
  CARGO_TERM_COLOR: always
  CARGO_REGISTRIES_CRATES_IO_PROTOCOL: sparse
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
  TURBO_REMOTE_ONLY: true # Enforces remote-only caching
  TURBO_CACHE: remote:rw # allow r/w to remote cache
  GH_TOKEN: ${{ secrets.GH_TOKEN }}
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  # Skip binary downloads during install
  YOUTUBE_DL_SKIP_DOWNLOAD: true
  ADBLOCK: true
  PUPPETEER_SKIP_DOWNLOAD: true
  CYPRESS_INSTALL_BINARY: 0
  PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1

jobs:
  # Common setup job to prepare repository
  setup:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

  # macOS Desktop Build
  build-macos:
    needs: setup
    runs-on: macos-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '23'

      - name: Install Bun
        uses: oven-sh/setup-bun@v2

      - name: Setup Rust
        run: |
          rustup update stable
          rustup default stable
          rustup target add aarch64-apple-darwin
          rustup target add x86_64-apple-darwin
        shell: bash

      - name: Create .npmrc to skip postinstall scripts
        run: |
          echo "ignore-scripts=true" > .npmrc
        shell: bash

      - name: Install dependencies
        run: bun install --no-postinstall
        env:
          NPM_CONFIG_IGNORE_SCRIPTS: true

      - name: Build packages
        run: bun run build

      - name: Build web assets for Tauri (macOS)
        run: |
          cd packages/app
          bun install --no-postinstall
          bun run build
        shell: bash

      - name: Install Tauri CLI
        run: |
          bun install -g @tauri-apps/cli
        shell: bash

      - name: Build the app (macOS Desktop)
        run: |
          cd packages/app
          bun run tauri build --target universal-apple-darwin
        shell: bash

      - name: Create GitHub release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            packages/app/src-tauri/target/universal-apple-darwin/release/bundle/dmg/**/*
          draft: ${{ github.event_name == 'push' }}
          prerelease: ${{ github.event.release.prerelease }}
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # Windows Desktop Build
  build-windows:
    needs: setup
    runs-on: windows-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '23'

      - name: Install Bun
        uses: oven-sh/setup-bun@v2

      - name: Setup Rust
        run: |
          rustup update stable
          rustup default stable
          rustup target add x86_64-pc-windows-msvc
        shell: bash

      - name: Install Windows dependencies (WebView2)
        run: |
          Invoke-WebRequest -Uri https://go.microsoft.com/fwlink/p/?LinkId=2124703 -OutFile ./MicrosoftEdgeWebview2Setup.exe
          Start-Process -FilePath ./MicrosoftEdgeWebview2Setup.exe -ArgumentList "/silent", "/install" -Wait
        shell: pwsh

      - name: Create .npmrc to skip postinstall scripts
        run: |
          echo "ignore-scripts=true" > .npmrc
        shell: bash

      - name: Install dependencies
        run: bun install --no-postinstall
        env:
          NPM_CONFIG_IGNORE_SCRIPTS: true

      - name: Build packages
        run: bun run build

      - name: Install Tauri CLI
        run: |
          bun install -g @tauri-apps/cli
        shell: bash

      - name: Build the app
        run: |
          cd packages/app
          bun run tauri build

      - name: Create GitHub release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            packages/app/src-tauri/target/release/bundle/nsis/**/*
          draft: ${{ github.event_name == 'push' }}
          prerelease: ${{ github.event.release.prerelease }}
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # Linux Desktop Build
  build-linux:
    needs: setup
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '23'

      - name: Install Bun
        uses: oven-sh/setup-bun@v2

      - name: Setup Rust
        run: |
          rustup update stable
          rustup default stable
          rustup target add x86_64-unknown-linux-gnu
        shell: bash

      - name: Install dependencies (ubuntu only)
        run: |
          sudo apt-get update
          sudo apt-get install -y libgtk-3-dev libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf libssl-dev libx11-dev libxdo-dev libxcb1-dev libxcb-render0-dev libxcb-shape0-dev libxcb-xfixes0-dev

      # - name: Create .npmrc to skip postinstall scripts
      #   run: |
      #     echo "ignore-scripts=true" > .npmrc
      #   shell: bash

      - name: Install dependencies
        run: bun install --no-postinstall
        env:
          NPM_CONFIG_IGNORE_SCRIPTS: true

      - name: Build packages
        run: bun run build

      - name: Install Tauri CLI
        run: |
          bun install -g @tauri-apps/cli
        shell: bash

      - name: Build the app
        run: |
          cd packages/app
          bun run tauri build

      - name: Collect Linux artifacts
        run: |
          mkdir -p ./release-artifacts
          cp packages/app/src-tauri/target/release/bundle/appimage/*.AppImage ./release-artifacts/ || echo "No AppImage found"
          cp packages/app/src-tauri/target/release/bundle/deb/*.deb ./release-artifacts/ || echo "No deb file found"

      - name: Create GitHub release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            ./release-artifacts/*
          draft: ${{ github.event_name == 'push' }}
          prerelease: ${{ github.event.release.prerelease }}
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # Android Build
  build-android:
    needs: setup
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '23'

      - name: Install Bun
        uses: oven-sh/setup-bun@v2

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Setup Android SDK/NDK
        uses: android-actions/setup-android@v3.0.0

      - name: Setup Rust
        run: |
          rustup update stable
          rustup default stable
          rustup target add aarch64-linux-android
          rustup target add armv7-linux-androideabi
          rustup target add i686-linux-android
          rustup target add x86_64-linux-android
        shell: bash

      - name: Install dependencies (ubuntu only)
        run: |
          sudo apt-get update
          sudo apt-get install -y libgtk-3-dev libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf libssl-dev libx11-dev libxdo-dev libxcb1-dev libxcb-render0-dev libxcb-shape0-dev libxcb-xfixes0-dev

      - name: Create .npmrc to skip postinstall scripts
        run: |
          echo "ignore-scripts=true" > .npmrc
        shell: bash

      - name: Install dependencies
        run: bun install --no-postinstall
        env:
          NPM_CONFIG_IGNORE_SCRIPTS: true

      - name: Build packages
        run: bun run build

      - name: Install Tauri CLI
        run: |
          bun install -g @tauri-apps/cli
        shell: bash

      - name: Build the Android app
        run: |
          cd packages/app
          # Create a dummy keystore for unsigned release builds if signing is not set up
          if [ ! -f "dummy.keystore" ]; then
            keytool -genkey -v -keystore dummy.keystore -alias dummyalias -keyalg RSA -keysize 2048 -validity 10000 -storepass dummypass -keypass dummypass -dname "CN=Dummy, OU=Dummy, O=Dummy, L=Dummy, S=Dummy, C=Dummy"
          fi
          export TAURI_KEY_PATH=./dummy.keystore
          export TAURI_KEY_ALIAS=dummyalias
          export TAURI_KEY_PASSWORD=dummypass

          bun run tauri android build --target aarch64 --release
        env:
          NPM_CONFIG_IGNORE_SCRIPTS: true
        shell: bash

      - name: Stage Android artifact for release
        run: |
          mkdir -p ./release-artifacts
          cp packages/app/src-tauri/gen/android/app/build/outputs/apk/release/app-release-unsigned.apk ./release-artifacts/app-aarch64-release.apk
        shell: bash

      - name: Create GitHub release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            ./release-artifacts/app-aarch64-release.apk
          draft: ${{ github.event_name == 'push' }}
          prerelease: ${{ github.event.release.prerelease }}
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # iOS Build
  build-ios:
    needs: setup
    runs-on: macos-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '23'

      - name: Install Bun
        uses: oven-sh/setup-bun@v2

      - name: Setup Rust
        run: |
          rustup update stable
          rustup default stable
          rustup target add aarch64-apple-ios
        shell: bash

      - name: Setup Apple Development Environment
        run: |
          echo "Placeholder for installing Apple certificates and provisioning profiles."
          echo "This step will require secrets like APPLE_DEVELOPMENT_CERTIFICATE_P12_BASE64, APPLE_CERTIFICATE_PASSWORD, APPLE_PROVISIONING_PROFILE_BASE64."
          # Example (actual commands depend on how secrets are stored and used):
          # echo "${{ secrets.APPLE_DEVELOPMENT_CERTIFICATE_P12_BASE64 }}" | base64 --decode > certificate.p12
          # security create-keychain -p tempci build.keychain
          # security default-keychain -s build.keychain
          # security unlock-keychain -p tempci build.keychain
          # security import certificate.p12 -k build.keychain -P "${{ secrets.APPLE_CERTIFICATE_PASSWORD }}" -A
          # security set-key-partition-list -S apple-tool:,apple: -s -k tempci build.keychain
          # mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          # echo "${{ secrets.APPLE_PROVISIONING_PROFILE_BASE64 }}" | base64 --decode > ~/Library/MobileDevice/Provisioning\ Profiles/ci_profile.mobileprovision
        shell: bash

      - name: Create .npmrc to skip postinstall scripts
        run: |
          echo "ignore-scripts=true" > .npmrc
        shell: bash

      - name: Install dependencies
        run: bun install --no-postinstall
        env:
          NPM_CONFIG_IGNORE_SCRIPTS: true

      - name: Build packages
        run: bun run build

      - name: Install Tauri CLI
        run: |
          bun install -g @tauri-apps/cli
        shell: bash

      - name: Build the iOS app
        run: |
          cd packages/app
          # Ensure correct Apple team ID is set in tauri.conf.json or via env vars for signing
          # export APPLE_DEVELOPMENT_TEAM="YOUR_TEAM_ID" # Example
          bun run tauri ios build --target aarch64-apple-ios --release
        env:
          # APPLE_SIGNING_IDENTITY: ${{ secrets.APPLE_SIGNING_IDENTITY }} # Example for actual signing
          # APPLE_PROVISIONING_PROFILE_UUID: ${{ secrets.APPLE_PROVISIONING_PROFILE_UUID }}
          # APPLE_DEVELOPMENT_TEAM: ${{ secrets.APPLE_DEVELOPMENT_TEAM }}
          NPM_CONFIG_IGNORE_SCRIPTS: true
        shell: bash

      - name: Stage iOS artifact for release
        run: |
          mkdir -p ./release-artifacts
          cp packages/app/src-tauri/gen/apple/DerivedData/App/Build/Products/Release-iphoneos/app.ipa ./release-artifacts/App-ios-aarch64-release.ipa
        shell: bash

      - name: Create GitHub release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            ./release-artifacts/App-ios-aarch64-release.ipa
          draft: ${{ github.event_name == 'push' }}
          prerelease: ${{ github.event.release.prerelease }}
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
