name: Build, Push & Deploy to Phala Cloud

on:
  push:
    branches:
      - main
    paths:
      - 'Dockerfile'
  workflow_dispatch:
    inputs:
      logLevel:
        description: 'Log level'
        required: true
        default: 'warning'
      environment:
        description: 'Environment to deploy'
        required: false
        default: 'staging'

env:
  APP_NAME: ${{ vars.APP_NAME || secrets.APP_NAME }}
  APP_ID: ${{ vars.APP_ID || secrets.APP_ID }}
  DOCKER_USERNAME: ${{ vars.DOCKER_USERNAME || secrets.DOCKER_USERNAME }}
  DOCKER_IMAGE: ${{ vars.DOCKER_IMAGE || secrets.DOCKER_IMAGE }}
  DOCKER_REGISTRY: ${{ vars.DOCKER_REGISTRY || secrets.DOCKER_REGISTRY || 'docker.io' }}
  DOCKER_REGISTRY_USERNAME: ${{ vars.DOCKER_REGISTRY_USERNAME || secrets.DOCKER_REGISTRY_USERNAME }}
  DOCKER_REGISTRY_PASSWORD: ${{ vars.DOCKER_REGISTRY_PASSWORD || secrets.DOCKER_REGISTRY_PASSWORD }}
  PHALA_CLOUD_API_KEY: ${{ vars.PHALA_CLOUD_API_KEY || secrets.PHALA_CLOUD_API_KEY }}
  OPENAI_API_KEY: ${{ vars.OPENAI_API_KEY || secrets.OPENAI_API_KEY }}
  ANTHROPIC_API_KEY: ${{ vars.ANTHROPIC_API_KEY || secrets.ANTHROPIC_API_KEY }}
  SERVER_PORT: ${{ vars.SERVER_PORT || secrets.SERVER_PORT }}
  POSTGRES_PASSWORD: ${{ vars.POSTGRES_PASSWORD || secrets.POSTGRES_PASSWORD }}
  POSTGRES_USER: ${{ vars.POSTGRES_USER || secrets.POSTGRES_USER }}
  POSTGRES_DB: ${{ vars.POSTGRES_DB || secrets.POSTGRES_DB }}
  EVM_CHAINS: ${{ vars.EVM_CHAINS || secrets.EVM_CHAINS }}
  BIRDEYE_API_KEY: ${{ vars.BIRDEYE_API_KEY || secrets.BIRDEYE_API_KEY }}
  COMMUNITY_MANAGER_DISCORD_APPLICATION_ID: ${{ vars.COMMUNITY_MANAGER_DISCORD_APPLICATION_ID || secrets.COMMUNITY_MANAGER_DISCORD_APPLICATION_ID }}
  COMMUNITY_MANAGER_DISCORD_API_TOKEN: ${{ vars.COMMUNITY_MANAGER_DISCORD_API_TOKEN || secrets.COMMUNITY_MANAGER_DISCORD_API_TOKEN }}
  SOCIAL_MEDIA_MANAGER_DISCORD_APPLICATION_ID: ${{ vars.SOCIAL_MEDIA_MANAGER_DISCORD_APPLICATION_ID || secrets.SOCIAL_MEDIA_MANAGER_DISCORD_APPLICATION_ID }}
  SOCIAL_MEDIA_MANAGER_DISCORD_API_TOKEN: ${{ vars.SOCIAL_MEDIA_MANAGER_DISCORD_API_TOKEN || secrets.SOCIAL_MEDIA_MANAGER_DISCORD_API_TOKEN }}
  LIAISON_DISCORD_APPLICATION_ID: ${{ vars.LIAISON_DISCORD_APPLICATION_ID || secrets.LIAISON_DISCORD_APPLICATION_ID }}
  LIAISON_DISCORD_API_TOKEN: ${{ vars.LIAISON_DISCORD_API_TOKEN || secrets.LIAISON_DISCORD_API_TOKEN }}
  PROJECT_MANAGER_DISCORD_APPLICATION_ID: ${{ vars.PROJECT_MANAGER_DISCORD_APPLICATION_ID || secrets.PROJECT_MANAGER_DISCORD_APPLICATION_ID }}
  PROJECT_MANAGER_DISCORD_API_TOKEN: ${{ vars.PROJECT_MANAGER_DISCORD_API_TOKEN || secrets.PROJECT_MANAGER_DISCORD_API_TOKEN }}
  DEV_REL_DISCORD_APPLICATION_ID: ${{ vars.DEV_REL_DISCORD_APPLICATION_ID || secrets.DEV_REL_DISCORD_APPLICATION_ID }}
  DEV_REL_DISCORD_API_TOKEN: ${{ vars.DEV_REL_DISCORD_API_TOKEN || secrets.DEV_REL_DISCORD_API_TOKEN }}
  INVESTMENT_MANAGER_DISCORD_APPLICATION_ID: ${{ vars.INVESTMENT_MANAGER_DISCORD_APPLICATION_ID || secrets.INVESTMENT_MANAGER_DISCORD_APPLICATION_ID }}
  INVESTMENT_MANAGER_DISCORD_API_TOKEN: ${{ vars.INVESTMENT_MANAGER_DISCORD_API_TOKEN || secrets.INVESTMENT_MANAGER_DISCORD_API_TOKEN }}
  POSTGRES_URL: ${{ vars.POSTGRES_URL || secrets.POSTGRES_URL }}
  TEE_MODE: ${{ vars.TEE_MODE || secrets.TEE_MODE || 'PRODUCTION' }}
  WALLET_SECRET_SALT: ${{ vars.WALLET_SECRET_SALT || secrets.WALLET_SECRET_SALT }}
  TEE_VENDOR: ${{ vars.TEE_VENDOR || secrets.TEE_VENDOR || 'phala' }}
  TELEGRAM_BOT_TOKEN: ${{ vars.TELEGRAM_BOT_TOKEN || secrets.TELEGRAM_BOT_TOKEN }}
jobs:
  build-and-push:
    permissions:
      contents: read
      packages: write
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ env.DOCKER_REGISTRY_USERNAME }}
          password: ${{ env.DOCKER_REGISTRY_PASSWORD }}

      - name: Build and Push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          push: true
          tags: |
            ${{ env.DOCKER_USERNAME }}/${{ env.DOCKER_IMAGE }}:latest
            ${{ env.DOCKER_USERNAME }}/${{ env.DOCKER_IMAGE }}:${{ github.sha }}
      - name: Set Docker Image Full Name
        run: |
          export DOCKER_IMAGE_FULL_NAME=${{ env.DOCKER_USERNAME }}/${{ env.DOCKER_IMAGE }}:${{ github.sha }}
      - name: Update Docker Compose
        run: |
          sed -i "s|\${DOCKER_IMAGE_FULL_NAME}|${{ env.DOCKER_USERNAME }}/${{ env.DOCKER_IMAGE }}:${{ github.sha }}|g" ./tee-docker-compose.yaml

      - name: Deploy to Phala Cloud
        uses: Leechael/phala-deploy-action@v2
        with:
          # Required parameters
          phala-api-key: ${{ secrets.PHALA_CLOUD_API_KEY }}

          # Optional parameters (with defaults)
          app-id: ${{ env.APP_ID || '' }} # App ID of existing CVM (if updating)
          cvm-name: ''
          compose-file: './tee-docker-compose.yaml' # Default: './docker-compose.yml'
          vcpu: '4' # Default: '2'
          memory: '4096' # Default: '2048'
          disk-size: '40' # Default: '40'
          envs: | # Environment variables in YAML format (will be converted to dotenv)
            DOCKER_REGISTRY_USERNAME: ${{ env.DOCKER_REGISTRY_USERNAME }}
            DOCKER_REGISTRY_PASSWORD: ${{ env.DOCKER_REGISTRY_PASSWORD }}
            DOCKER_REGISTRY: ${{ env.DOCKER_REGISTRY }}
            OPENAI_API_KEY: ${{ env.OPENAI_API_KEY }}
            ANTHROPIC_API_KEY: ${{ env.ANTHROPIC_API_KEY }}
            SERVER_PORT: ${{ env.SERVER_PORT }}
            EVM_CHAINS: ${{ env.EVM_CHAINS }}
            BIRDEYE_API_KEY: ${{ env.BIRDEYE_API_KEY }}
            COMMUNITY_MANAGER_DISCORD_APPLICATION_ID: ${{ env.COMMUNITY_MANAGER_DISCORD_APPLICATION_ID }}
            COMMUNITY_MANAGER_DISCORD_API_TOKEN: ${{ vars.COMMUNITY_MANAGER_DISCORD_API_TOKEN || secrets.COMMUNITY_MANAGER_DISCORD_API_TOKEN }}
            SOCIAL_MEDIA_MANAGER_DISCORD_APPLICATION_ID: ${{ env.SOCIAL_MEDIA_MANAGER_DISCORD_APPLICATION_ID }}
            SOCIAL_MEDIA_MANAGER_DISCORD_API_TOKEN: ${{ env.SOCIAL_MEDIA_MANAGER_DISCORD_API_TOKEN }}
            LIAISON_DISCORD_APPLICATION_ID: ${{ env.LIAISON_DISCORD_APPLICATION_ID }}
            LIAISON_DISCORD_API_TOKEN: ${{ env.LIAISON_DISCORD_API_TOKEN }}
            PROJECT_MANAGER_DISCORD_APPLICATION_ID: ${{ env.PROJECT_MANAGER_DISCORD_APPLICATION_ID }}
            PROJECT_MANAGER_DISCORD_API_TOKEN: ${{ env.PROJECT_MANAGER_DISCORD_API_TOKEN }}
            DEV_REL_DISCORD_APPLICATION_ID: ${{ env.DEV_REL_DISCORD_APPLICATION_ID }}
            DEV_REL_DISCORD_API_TOKEN: ${{ env.DEV_REL_DISCORD_API_TOKEN }}
            INVESTMENT_MANAGER_DISCORD_APPLICATION_ID: ${{ env.INVESTMENT_MANAGER_DISCORD_APPLICATION_ID }}
            INVESTMENT_MANAGER_DISCORD_API_TOKEN: ${{ env.INVESTMENT_MANAGER_DISCORD_API_TOKEN }}
            POSTGRES_URL: ${{ env.POSTGRES_URL }}
            TEE_MODE: ${{ env.TEE_MODE || 'PRODUCTION' }}
            WALLET_SECRET_SALT: ${{ env.WALLET_SECRET_SALT }}
            TEE_VENDOR: ${{ env.TEE_VENDOR || 'phala' }}
            TELEGRAM_BOT_TOKEN: ${{ env.TELEGRAM_BOT_TOKEN }}
          node-id: '' # Node ID (Teepod ID: Default to available node id)
          base-image: '' # Base image to use for the CVM (Default to latest dstack image)
