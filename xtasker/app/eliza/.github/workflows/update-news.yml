name: Update News Files

on:
  schedule:
    - cron: '0 0 * * *' # Run at midnight UTC daily
  workflow_dispatch: # Allow manual trigger

jobs:
  update-news:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          ref: autodocs
          fetch-depth: 0

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y jq
          bun install

      - name: Make script executable
        run: chmod +x scripts/update-news.sh

      - name: Run update script
        run: ./scripts/update-news.sh

      - name: Build and Deploy Docs
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          cd packages/docs
          bun install
          bun run deploy

      - name: Run repomix
        run: |
          npx repomix -c scripts/repomix.config.json
          npx repomix --include="packages/docs/api/**/*.md" --style markdown --no-gitignore -o packages/docs/static/llms-api.md

      - name: Commit and push if there are changes
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
          git add packages/docs/news/ scripts/repomix.config.json packages/docs/static/llms-*.txt
          git diff --quiet && git diff --staged --quiet || (git commit -m 'chore: update news files and llms context' && git push)
