{"name": "@elizaos/api-client", "version": "1.3.1", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/index.js", "description": "Type-safe API client for ElizaOS server", "type": "module", "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "bun test", "test:watch": "bun test --watch", "lint": "prettier --write ./src", "typecheck": "tsc --noEmit"}, "dependencies": {"@elizaos/core": "1.3.1"}, "devDependencies": {"@types/bun": "latest", "@types/node": "latest", "eslint": "^8.57.0", "tsup": "^8.5.0", "typescript": "^5.7.2"}, "files": ["dist"], "publishConfig": {"access": "public"}, "gitHead": "48d0b08182b5fd5b17dd58198b1fee15b0815775"}