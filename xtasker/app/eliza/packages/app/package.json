{"name": "@elizaos/app", "version": "1.3.1", "private": true, "type": "module", "scripts": {"start": "tauri dev", "dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "test": "bun test src/__tests__", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "typecheck": "tsc --noEmit", "format": "prettier --write ./src", "format:check": "prettier --check ./src", "lint": "prettier --write ./src"}, "dependencies": {"@elizaos/api-client": "workspace:*", "@elizaos/cli": "workspace:*", "@tauri-apps/api": "^2.6.0", "@tauri-apps/plugin-opener": "^2.4.0", "@tauri-apps/plugin-shell": "^2.3.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@tauri-apps/cli": "^2.6.2", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "prettier": "3.5.3", "typescript": "5.8.2", "vite": "^6.3.5"}, "gitHead": "b165ad83e5f7a21bc1edbd83374ca087e3cd6b33"}