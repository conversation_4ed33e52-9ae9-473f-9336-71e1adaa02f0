{"$schema": "https://schema.tauri.app/config/2", "productName": "<PERSON>", "version": "1.2.4", "identifier": "com.elizaos.desktop", "build": {"beforeDevCommand": "bun run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "bun run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "<PERSON>", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600}], "security": {"csp": {"default-src": "'self'", "img-src": "'self' data: asset: https://asset.localhost http://localhost:3000", "connect-src": "'self' http://localhost:3000 ws://localhost:3000 http://localhost:* https://localhost:* capacitor://* tauri://* https://api.eliza.how", "style-src": "'self' 'unsafe-inline' http://localhost:3000", "script-src": "'self' 'unsafe-eval' http://localhost:3000", "frame-src": "'self' http://localhost:3000"}}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "plugins": {"shell": {"open": true}}}