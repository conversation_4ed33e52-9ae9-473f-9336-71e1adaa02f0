{"type": "module", "name": "@elizaos/autodoc", "version": "1.3.1", "private": true, "description": "", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "scripts": {"build": "tsup", "autodoc:dev": "tsup --watch", "autodoc": "node dist/index.js", "clean": "rm -rf dist .turbo node_modules .turbo-tsconfig.json tsconfig.tsbuildinfo", "lint": "prettier --write ./src", "test": "echo \"No tests configured for @elizaos/autodoc\"", "format": "prettier --write ./src", "format:check": "prettier --check ./src"}, "keywords": [], "author": "", "license": "MIT", "dependencies": {"@langchain/openai": "^0.3.16", "@octokit/rest": "^21.0.2", "@types/node": "^22.15.3", "@typescript-eslint/parser": "8.26.0", "@typescript-eslint/types": "8.26.0", "@typescript-eslint/typescript-estree": "8.26.0", "dotenv": "^16.4.7", "langchain": "^0.3.7", "ts-node": "10.9.2", "tslib": "2.8.1", "tsup": "8.5.0", "typescript": "5.8.2", "yaml": "^2.3.4"}, "devDependencies": {"prettier": "3.5.3", "ts-node": "10.9.2", "tslib": "2.8.1", "tsup": "8.5.0", "typescript": "5.8.2"}, "gitHead": "dc32785668d1663c0e7fb81909b4b9961db6e6de"}