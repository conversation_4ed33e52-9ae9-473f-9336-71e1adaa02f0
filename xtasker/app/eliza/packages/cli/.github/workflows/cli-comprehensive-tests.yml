name: CLI Comprehensive Tests

on:
  push:
    branches: [main, develop]
    paths:
      - 'packages/cli/**'
      - '.github/workflows/cli-comprehensive-tests.yml'
  pull_request:
    paths:
      - 'packages/cli/**'

jobs:
  test-matrix:
    name: Test on ${{ matrix.os }} / Node ${{ matrix.node }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        node: [18, 20, 22]

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install BATS (Unix)
        if: runner.os != 'Windows'
        run: |
          if [[ "$RUNNER_OS" == "macOS" ]]; then
            brew install bats-core
          else
            sudo apt-get update
            sudo apt-get install -y bats
          fi

      - name: Install dependencies
        run: bun install

      - name: Build CLI
        run: bun run build
        working-directory: packages/cli

      - name: Run TypeScript checks
        run: bun run test:types
        working-directory: packages/cli

      - name: Run unit tests with coverage
        run: bun run test:unit:coverage
        working-directory: packages/cli

      - name: Run BATS tests (Unix)
        if: runner.os != 'Windows'
        run: bun run test:bats
        working-directory: packages/cli

      - name: Test global installation
        run: |
          cd packages/cli
          npm pack
          npm install -g ./elizaos-cli-*.tgz
          elizaos --version
          npm uninstall -g @elizaos/cli

      - name: Upload coverage
        if: matrix.os == 'ubuntu-latest' && matrix.node == '20'
        uses: codecov/codecov-action@v3
        with:
          directory: packages/cli/coverage
          flags: cli

  integration-test:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: test-matrix

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1

      - name: Install BATS
        run: |
          sudo apt-get update
          sudo apt-get install -y bats

      - name: Install dependencies
        run: bun install

      - name: Build packages
        run: bun run build

      - name: Run integration tests
        run: |
          cd packages/cli
          bats tests/bats/integration

      - name: Test with published packages simulation
        run: |
          cd packages/cli
          npm pack
          cd /tmp
          npm init -y
          npm install $GITHUB_WORKSPACE/packages/cli/elizaos-cli-*.tgz
          npx elizaos create test-project --no-install
          cd test-project
          ls -la

  performance-test:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: test-matrix

    steps:
      - uses: actions/checkout@v4

      - name: Setup environment
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1

      - name: Install dependencies
        run: bun install

      - name: Build CLI
        run: |
          cd packages/cli
          bun run build

      - name: Run performance benchmarks
        run: |
          cd packages/cli
          # Measure CLI startup time
          time node dist/index.js --version

          # Measure command execution time
          time node dist/index.js create perf-test --no-install

          # Measure build time
          cd perf-test
          time bun run build || true

      - name: Upload benchmark results
        uses: actions/upload-artifact@v4
        with:
          name: performance-results
          path: packages/cli/benchmark-results.json

  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: |
          cd packages/cli
          npm audit --production

      - name: Check for known vulnerabilities
        uses: snyk/actions/node@master
        continue-on-error: true
        with:
          args: --severity-threshold=high
