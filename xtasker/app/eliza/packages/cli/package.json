{"name": "@elizaos/cli", "version": "1.3.1", "description": "elizaOS CLI - Manage your AI agents and plugins", "publishConfig": {"access": "public", "workspaces": {"preserveIfPresent": true}}, "license": "MIT", "author": {"name": "eliza<PERSON>", "url": "https://twitter.com/eliza_OS"}, "repository": {"type": "git", "url": "https://github.com/elizaOS/eliza.git", "directory": "packages/cli"}, "files": ["dist", "dist/**/.giti<PERSON>re", "dist/**/.npm<PERSON><PERSON>", "templates", "templates/**/.gitignore", "templates/**/.npmignore"], "keywords": [], "type": "module", "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "bin": {"elizaos": "./dist/index.js"}, "scripts": {"start": "bun run build && bun dist/index.js start", "build": "bun run src/scripts/copy-templates.ts && bun x tsup", "lint": "prettier --write ./src", "format": "prettier --write ./src", "format:check": "prettier --check ./src", "clean": "rm -rf dist .turbo node_modules .turbo-tsconfig.json tsconfig.tsbuildinfo", "test": "bun run test:all", "test:all": "./run-all-tests.sh", "test:unit": "bun test --timeout 30000", "test:unit:watch": "bun test --watch --timeout 30000", "test:unit:coverage": "bun test --coverage --timeout 30000", "test:bats": "bats tests/bats", "test:bats:commands": "bats tests/bats/commands", "test:bats:integration": "bats tests/bats/integration", "test:bats:e2e": "bats tests/bats/e2e", "test:types": "tsc --noEmit", "coverage": "c8 --reporter=lcov --reporter=text bun test", "coverage:html": "c8 --reporter=html bun test && open coverage/index.html", "test:cli": "cross-env NODE_OPTIONS=\"--max-old-space-size=4096\" bun test tests/commands/ --timeout 120000; EXIT_CODE=$?; bash tests/cleanup-processes.sh; exit $EXIT_CODE"}, "devDependencies": {"@types/bun": "^1.2.17", "@types/express": "^5.0.2", "@types/fs-extra": "^11.0.1", "@types/helmet": "^4.0.0", "@types/node": "^24.0.3", "@types/prompts": "^2.4.2", "@types/semver": "^7.5.8", "bats-assert": "^2.0.0", "bats-support": "^0.3.0", "cross-env": "^7.0.3", "esbuild-plugin-copy": "^2.1.1", "prettier": "3.5.3", "tsup": "8.5.0", "typescript": "5.8.3", "vite": "^6.3.5"}, "gitHead": "48d0b08182b5fd5b17dd58198b1fee15b0815775", "dependencies": {"@anthropic-ai/claude-code": "^1.0.35", "@anthropic-ai/sdk": "^0.54.0", "@clack/prompts": "^0.11.0", "@elizaos/core": "1.3.1", "@elizaos/plugin-sql": "1.3.1", "@elizaos/server": "1.3.1", "bun": "^1.2.17", "chalk": "^5.3.0", "chokidar": "^4.0.3", "commander": "^14.0.0", "dotenv": "^16.5.0", "fs-extra": "^11.1.0", "globby": "^14.0.2", "https-proxy-agent": "^7.0.6", "ora": "^8.1.1", "rimraf": "6.0.1", "semver": "^7.7.2", "simple-git": "^3.27.0", "tiktoken": "^1.0.18", "tsconfig-paths": "^4.2.0", "type-fest": "^4.41.0", "yoctocolors": "^2.1.1", "zod": "^3.25.67"}}