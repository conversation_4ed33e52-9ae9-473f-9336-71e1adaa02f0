{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"isolatedModules": false, "baseUrl": ".", "module": "ESNext", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "target": "ESNext", "paths": {"@/src/*": ["./src/*"], "@elizaos/core": ["../../core/src"], "@elizaos/core/*": ["../../core/src/*"]}, "rootDir": ".", "moduleResolution": "bundler", "outDir": "./dist", "jsx": "react", "lib": ["ES2022", "DOM", "DOM.Iterable"], "skipLibCheck": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist"]}