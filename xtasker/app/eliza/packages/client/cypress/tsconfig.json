{"extends": "../tsconfig.app.json", "compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": true, "jsx": "react-jsx", "strict": true, "skipLibCheck": true, "baseUrl": "../", "paths": {"@/*": ["./src/*"], "@elizaos/core": ["../core/src"], "@elizaos/core/*": ["../core/src/*"]}, "types": ["cypress", "node", "@testing-library/cypress", "@cypress/react"], "esModuleInterop": true, "allowSyntheticDefaultImports": true}, "include": ["**/*.ts", "**/*.tsx", "**/*.d.ts", "./support/**/*", "../src/**/*.cy.ts", "../src/**/*.cy.tsx", "../src/**/*.tsx", "../src/**/*.ts"], "exclude": ["node_modules"]}