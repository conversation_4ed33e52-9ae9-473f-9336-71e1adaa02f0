{"name": "@elizaos/client", "version": "1.3.1", "private": true, "sideEffects": false, "type": "module", "scripts": {"start:client": "vite", "dev:client": "vite --host 0.0.0.0 --port 5173", "build": "vite build", "preview": "vite preview", "lint": "prettier --write ./src", "format": "prettier --write ./src", "format:check": "prettier --check ./src", "clean": "rm -rf dist .turbo node_modules .turbo-tsconfig.json tsconfig.tsbuildinfo", "test": "bun test", "test:all": "./scripts/test-all.sh", "test:quick": "./scripts/test-quick.sh", "test:unit": "./scripts/run-bun-tests.sh --coverage", "test:unit:watch": "./scripts/run-bun-tests.sh --watch", "test:component": "cypress run --component", "test:e2e": "cypress run --e2e", "test:e2e:with-server": "./scripts/test-e2e-with-server.sh", "cypress:open": "cypress open", "type-check": "cd cypress && npx tsc --noEmit --project tsconfig.json"}, "dependencies": {"@elizaos/api-client": "workspace:*", "@elizaos/core": "workspace:*", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@react-spring/web": "^9.7.5", "@tanstack/react-query": "^5.67.2", "@uidotdev/usehooks": "^2.4.1", "class-variance-authority": "^0.7.1", "clsx": "2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "evt": "^2.5.9", "lucide-react": "^0.469.0", "react": "^19.1.0", "react-aiwriter": "^1.0.0", "react-dom": "^19.1.0", "react-force-graph": "^1.47.6", "react-force-graph-2d": "^1.27.1", "react-joyride": "^2.9.3", "react-resizable-panels": "^2.1.7", "react-router": "^7.3.0", "react-router-dom": "^7.3.0", "remark-breaks": "^4.0.0", "semver": "^7.7.1", "shiki": "^3.6.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-stick-to-bottom": "^1.1.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-node-polyfills": "0.17.0"}, "devDependencies": {"@cypress/react": "^9.0.1", "@cypress/vite-dev-server": "^6.0.3", "@eslint/js": "^9.22.0", "@happy-dom/global-registrator": "^18.0.1", "@playwright/test": "^1.40.0", "@radix-ui/react-direction": "^1.1.1", "@tailwindcss/vite": "^4.1.0", "@testing-library/cypress": "^10.0.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.0.0", "@testing-library/react-hooks": "^8.0.1", "@types/cypress": "^1.1.6", "@types/node": "^24.0.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/semver": "^7.5.8", "@typescript-eslint/eslint-plugin": "8.26.0", "@typescript-eslint/parser": "8.26.0", "@vitejs/plugin-react-swc": "^3.8.0", "asn1.js": "^5.4.1", "assert": "^2.1.0", "bn.js": "^5.2.2", "browserify-aes": "^1.2.0", "browserify-cipher": "^1.0.1", "browserify-des": "^1.0.2", "browserify-sign": "^4.2.3", "buffer": "^6.0.3", "cipher-base": "^1.0.6", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "crypto-browserify": "^3.12.1", "cypress": "^14.4.1", "cypress-real-events": "^1.14.0", "des.js": "^1.1.0", "diffie-hellman": "^5.0.3", "elliptic": "^6.6.1", "eslint": "^9.22.0", "eslint-import-resolver-typescript": "^3.8.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "evp_bytestokey": "^1.0.3", "globals": "^15.15.0", "https-browserify": "^1.0.0", "js-sha1": "^0.7.0", "js-sha256": "^0.11.1", "jsdom": "^26.1.0", "md5.js": "^1.3.5", "miller-rabin": "^4.0.1", "os-browserify": "^0.3.0", "parse-asn1": "^5.1.7", "pbkdf2": "^3.1.2", "postcss": "^8.5.3", "prettier": "3.5.3", "process": "^0.11.10", "public-encrypt": "^4.0.3", "randombytes": "^2.1.0", "randomfill": "^1.0.4", "readable-stream": "^4.7.0", "rollup-plugin-visualizer": "^5.14.0", "safe-buffer": "^5.2.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "tailwindcss": "^4.1.0", "typescript": "5.8.2", "typescript-eslint": "^8.26.0", "url": "^0.11.4", "util": "^0.12.5", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4", "wait-on": "^8.0.3"}}