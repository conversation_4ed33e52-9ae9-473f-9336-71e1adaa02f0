# Build outputs
dist/
node_modules/

# Environment files
.env
.env.local
.env.production
.env.staging
.env.development
.env.bak
*.env

# OS files
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Cache directories
.cache/
.npm/
.eslintcache

# Temporary folders
tmp/
temp/

# Database files
*.db
*.pglite
*.pglite3

# ElizaOS specific
.eliza/
.elizadb/
pglite/
cache/

generatedImages/
images/
data/
.eliza
.elizadb-test

dist/
coverage/