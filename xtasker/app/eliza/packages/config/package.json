{"name": "@elizaos/config", "description": "Shared configuration for ElizaOS projects and plugins", "version": "1.3.1", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "packageType": "plugin", "platform": "node", "license": "MIT", "author": "eliza<PERSON>", "keywords": ["plugin", "<PERSON><PERSON><PERSON>"], "repository": {"type": "git", "url": "https://github.com/elizaos/eliza.git", "directory": "packages/config"}, "homepage": "https://eliza.how", "bugs": {"url": "https://github.com/elizaos/eliza/issues"}, "exports": {"./eslint/eslint.config.plugin.js": "./src/eslint/eslint.config.plugin.js", "./eslint/eslint.config.base.js": "./src/eslint/eslint.config.base.js", "./eslint/eslint.config.frontend.js": "./src/eslint/eslint.config.frontend.js", "./prettier/prettier.config.js": "./src/prettier/prettier.config.js", "./typescript/tsconfig.base.json": "./src/typescript/tsconfig.base.json", "./typescript/tsconfig.plugin.json": "./src/typescript/tsconfig.plugin.json", "./typescript/tsconfig.frontend.json": "./src/typescript/tsconfig.frontend.json", "./typescript/tsconfig.test.json": "./src/typescript/tsconfig.test.json"}, "files": ["dist", "src"], "devDependencies": {"prettier": "^3.5.3", "typescript": "^5.8.2"}, "scripts": {"build": "tsup", "lint": "prettier --write ./src", "format": "prettier --write ./src", "format:check": "prettier --check ./src"}, "publishConfig": {"access": "public"}, "gitHead": "48d0b08182b5fd5b17dd58198b1fee15b0815775"}