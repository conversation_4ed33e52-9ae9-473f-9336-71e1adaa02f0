/**
 * Standard Prettier configuration for ElizaOS packages
 * Provides consistent code formatting across the monorepo
 */
export default {
  // Basic formatting options
  printWidth: 100,
  tabWidth: 2,
  useTabs: false,
  semi: true,
  singleQuote: true,
  quoteProps: 'as-needed',
  jsxSingleQuote: false,
  trailingComma: 'es5',
  bracketSpacing: true,
  bracketSameLine: false,
  arrowParens: 'always',

  // Prose formatting
  proseWrap: 'preserve',

  // HTML/JSX formatting
  htmlWhitespaceSensitivity: 'css',
  vueIndentScriptAndStyle: false,

  // End of line
  endOfLine: 'lf',

  // Embedded language formatting
  embeddedLanguageFormatting: 'auto',

  // Plugin specific overrides
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 120,
      },
    },
    {
      files: '*.md',
      options: {
        printWidth: 80,
        proseWrap: 'always',
      },
    },
    {
      files: '*.{yaml,yml}',
      options: {
        tabWidth: 2,
      },
    },
  ],
};
