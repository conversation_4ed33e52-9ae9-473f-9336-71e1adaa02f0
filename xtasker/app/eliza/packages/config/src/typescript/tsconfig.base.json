{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ES2022", "moduleResolution": "bundler", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "allowJs": true, "checkJs": false, "noEmitOnError": false, "moduleDetection": "force", "allowArbitraryExtensions": true, "types": ["node"], "baseUrl": "../../../", "paths": {"@elizaos/core": ["packages/core/src"], "@elizaos/core/*": ["packages/core/src/*"], "@elizaos/client": ["packages/client/src"], "@elizaos/client/*": ["packages/client/src/*"], "@elizaos/server": ["packages/server/src"], "@elizaos/server/*": ["packages/server/src/*"], "@elizaos/api-client": ["packages/api-client/src"], "@elizaos/api-client/*": ["packages/api-client/src/*"], "@elizaos/cli": ["packages/cli/src"], "@elizaos/cli/*": ["packages/cli/src/*"], "@elizaos/plugin-sql": ["packages/plugin-sql/src"], "@elizaos/plugin-sql/*": ["packages/plugin-sql/src/*"], "@elizaos/plugin-bootstrap": ["packages/plugin-bootstrap/src"], "@elizaos/plugin-bootstrap/*": ["packages/plugin-bootstrap/src/*"], "@elizaos/plugin-dummy-services": ["packages/plugin-dummy-services/src"], "@elizaos/plugin-dummy-services/*": ["packages/plugin-dummy-services/src/*"], "@elizaos/autodoc": ["packages/autodoc/src"], "@elizaos/autodoc/*": ["packages/autodoc/src/*"], "@elizaos/app": ["packages/app/src"], "@elizaos/app/*": ["packages/app/src/*"], "@elizaos/config": ["packages/config/src"], "@elizaos/config/*": ["packages/config/src/*"]}}}