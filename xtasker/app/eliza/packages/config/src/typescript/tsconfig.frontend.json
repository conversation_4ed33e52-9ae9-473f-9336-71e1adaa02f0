{"$schema": "https://json.schemastore.org/tsconfig", "extends": "./tsconfig.base.json", "compilerOptions": {"outDir": "dist", "lib": ["ESNext", "DOM", "DOM.Iterable"], "jsx": "react-jsx", "declaration": true, "emitDeclarationOnly": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/__tests__/**/*.ts", "__tests__/**/*.ts"], "exclude": ["node_modules", "dist", "src/**/*.test.ts", "src/**/*.spec.ts", "**/*.test.ts", "**/*.spec.ts", "**/*.test.tsx", "**/*.spec.tsx"]}