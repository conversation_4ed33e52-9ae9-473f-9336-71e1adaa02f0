{"compilerOptions": {"outDir": "dist", "lib": ["ESNext", "dom"], "target": "ESNext", "module": "Preserve", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": false, "allowImportingTsExtensions": true, "emitDeclarationOnly": true, "declaration": true, "resolveJsonModule": true, "noImplicitAny": false, "allowJs": true, "checkJs": false, "noEmitOnError": false, "moduleDetection": "force", "allowArbitraryExtensions": true, "jsx": "react", "isolatedModules": false, "noEmit": true}, "include": ["src/**/*.ts"], "exclude": ["src/__tests__/**/*", "e2e/**/*", "**/*.test.ts", "**/*.spec.ts"]}