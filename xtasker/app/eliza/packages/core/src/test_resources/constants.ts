import type { UUID } from '@elizaos/core';

export const SERVER_URL = 'http://localhost:7998';
export const SUPABASE_URL = 'https://pronvzrzfwsptkojvudd.supabase.co';
export const SUPABASE_ANON_KEY =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InByb252enJ6ZndzcHRrb2p2dWRkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDY4NTYwNDcsImV4cCI6MjAyMjQzMjA0N30.I6_-XrqssUb2SWYg5DjsUqSodNS3_RPoET3-aPdqywM';
export const TEST_EMAIL = '<EMAIL>';
export const TEST_PASSWORD = 'mock_password_123!@#';
export const TEST_EMAIL_2 = '<EMAIL>';
export const TEST_PASSWORD_2 = 'mock_password_234!@#';

export const zeroUuid = '00000000-0000-0000-0000-000000000000' as UUID;
