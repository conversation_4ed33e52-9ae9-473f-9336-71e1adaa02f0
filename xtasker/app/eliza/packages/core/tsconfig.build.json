{"compilerOptions": {"target": "ES2021", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "skipLibCheck": true, "lib": ["ES2021.String"], "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "rootDir": "./src", "outDir": "./dist", "declaration": true, "emitDeclarationOnly": true, "noEmit": false, "sourceMap": true, "typeRoots": ["./node_modules/@types"]}, "include": ["src/**/*.ts"]}