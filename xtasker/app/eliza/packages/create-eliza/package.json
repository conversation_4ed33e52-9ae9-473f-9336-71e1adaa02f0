{"name": "create-eliza", "version": "1.3.1", "description": "Initialize an Eliza project", "type": "module", "private": true, "publishConfig": {"access": "public"}, "bin": {"create-eliza": "index.mjs"}, "scripts": {"create-eliza": "bun run index.mjs", "test": "echo \"No tests configured for create-eliza\""}, "license": "MIT", "author": {"name": "eliza<PERSON>", "url": "https://twitter.com/eliza_OS"}, "dependencies": {"@elizaos/cli": "1.2.10"}, "repository": {"type": "git", "url": "https://github.com/elizaOS/eliza.git", "directory": "packages/create-eliza"}, "files": ["cli", "index.mjs", "package.json"], "keywords": ["eliza", "<PERSON><PERSON><PERSON>", "ai", "agent", "create"], "engines": {"node": ">=23.3.0"}, "gitHead": "d5bd5c43bfebeb7ac02f9e029f924cb6cd5c2ec7", "devDependencies": {"prettier": "3.5.3"}}