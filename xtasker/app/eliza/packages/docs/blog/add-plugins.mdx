---
title: Adding Plugins in V2
authors: jin
date: 2025-04-13
tags: [plugins, registry, elizaos, announcement, v2]
---

V2 introduces significant upgrades to improve how developers build, share, and integrate functionality into their agents. This guide will walk through updates as they relate to plugins (hint: everything is a plugin).

{/* truncate */}

## What's New

The codebase is more compact in order to be easier to work with. V2 also features a formal API specification for better definition and compatibility. When it comes to plugins, here's what's new:

- **No central repo needed**: Host your plugin in your own GitHub repo
- **One-line integration**: Add plugins to your `package.json` and `agent.json`.
- **Open registry**: Submit a PR to get listed on the [Plugin Registry](https://elizaos.github.io/registry).
- **Dynamic loading**: Load plugins from external repositories

Many of these improvements were already being adopted in V1 toward the end of its lifecycle in order to create a natural evolution path for developers already familiar with the platform.

### 🛠 Plugin Architecture

ElizaOS V2 uses a unified plugin architecture where everything is a plugin:

```typescript
type Plugin = {
  name: string;
  description: string;
  clients?: Client[];
  adapters?: Adapter[];
  actions?: Action[];
  services?: Service[];
  providers?: Provider[];
  evaluators?: Evaluator[];
};
```

This means you can extend virtually any aspect of your agent's functionality:

- Platform connections (Discord, Twitter, Telegram)
- Storage options (PostgreSQL, MongoDB, Redis)
- Action capabilities (what your agent can do)
- Service providers (background processes and specialized capabilities)
- Evaluation components (how your agent makes decisions)

---

## 🧩 How to Add a Plugin in V2

Here are steps on how to add your plugin:

### 1. Package.json Integration

```bash
# Install plugin directly from GitHub
npm install @elizaos/plugin-example@github:your-org/plugin-example

# Or update package.json manually
"dependencies": {
  "@elizaos/plugin-example": "github:your-org/plugin-example"
}
```

### 2. Agent Configuration

```json
{
  "name": "MyAgent",
  "plugins": ["@elizaos/plugin-example"]
}
```

The plugin will be automatically loaded when your agent starts.

---

## 🖥️ CLI Plugins Commands

The ElizaOS CLI provides several commands to manage plugins. To install the CLI:

:::tip

```bash
bun install -g @elizaos/cli
```

:::

### Plugin Management Commands

```bash
# List all plugins available in the registry
elizaos plugins list

# List plugins currently installed in your project
elizaos plugins installed-plugins

# Add a plugin to your project (with options)
elizaos plugins add <plugin-name> [--no-env-prompt] [--branch <branchName>] [--tag <tagname>]

# Remove a plugin from your project
elizaos plugins remove <plugin-name>
```

- `elizaos plugins list` (aliases: `ls`, `l`): Lists available plugins from the registry.
- `elizaos plugins installed-plugins`: Lists plugins found in your project's dependencies.
- `elizaos plugins add <plugin>` (alias: `install`): Adds a plugin to your project. Options:
  - `--no-env-prompt`: Skip prompting for environment variables.
  - `--branch <branchName>`: Specify a branch to install from when using a monorepo source (default: `main`).
  - `-T, --tag <tagname>`: Specify a package tag to install (e.g., `latest`).
- `elizaos plugins remove <plugin>` (aliases: `delete`, `del`, `rm`): Removes a plugin from your project and cleans up files.

### Publishing Plugins

To publish your own plugin to the registry:

```bash
elizaos publish
```

This command will guide you through publishing your plugin, including updating the registry and optionally publishing to npm or GitHub. For more details, see the Publishing section or run `elizaos publish --help`.

These commands streamline plugin discovery, installation, removal, and distribution.

---

## 📂 Creating Your Own Plugin

The minimal structure for a V2 plugin repository:

```typescript
plugin-example/
├── assets/
│   ├── logo.png       // 400x400 branding
│   ├── banner.png     // 1280x640 banner
│   └── screenshots/   // Usage examples
├── src/
│   ├── index.ts       // Plugin entry point
│   └── ... (plugin code)
├── package.json       // With agentConfig section
└── README.md          // Documentation
```

Your `package.json` should include plugin metadata:

```json
"agentConfig": {
  "pluginType": "elizaos:plugin:1.0.0",
  "pluginParameters": {
    "API_KEY": {
      "type": "string",
      "description": "API key for the service"
    }
  }
}
```

---

## 📬 Submit to the Registry

To get your plugin listed:

1. Host your plugin on GitHub
2. Add branding, docs, and working examples
3. Submit a [PR to the registry](https://github.com/elizaos-plugins/registry)

Since the plugin lives in your repo you can start using it right away. The registry mainly helps with discovery, just know the review process typically takes up to a week so be prepared for that.

The ElizaOS V2 ecosystem already includes plugins for:

- Blockchain integration (Solana, Ethereum)
- Social media (Twitter, Discord, Telegram)
- Data processing (PDF, Image, Video)
- External APIs (Browser, Web Search)
- Local LLM deployment (Llama)

> See the plugin showcase here: https://eliza.how/packages

![](/img/montage-plugins.jpg)

### More Links

- [elizaos-plugins.github.io/registry](https://elizaos-plugins.github.io/registry)
- [Packages explorer](packages)
- [Plugin Guide](https://eliza.how/docs/plugins)
