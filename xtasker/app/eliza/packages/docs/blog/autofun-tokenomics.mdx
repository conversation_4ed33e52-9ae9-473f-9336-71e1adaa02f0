---
slug: autofun-tokenomics
title: Autofun Tokenomics
authors: [team]
tags: [autofun, tokenomics, ai16z, liquidity, launchpad, elizaos]
image: /img/autofun-tokenomics.jpg
---

# Autofun Tokenomics

auto.fun tokenomics: value accrual is fun

## Overview

[auto.fun](https://auto.fun/) is the first token launch platform purpose-built for agent-native ecosystems. Designed and maintained by Eliza Labs, it offers a low-friction, high-alignment environment for launching new tokens and/or autonomous agents built on the ElizaOS stack.

![Image](https://pbs.twimg.com/media/Go5bNx5XsAAlIIs?format=jpg&name=900x900)
(https://x.com/autodotfun/article/1915889326484766781/media/1913567183969431552)

{/* truncate */}

Our long-term tokenomics architecture centers around a dual-pool system that compounds value as more agents are deployed. While some components are still being actively developed, key mechanisms are already operational—most notably a swap fee-sharing system collaboratively built with Raydium, and buybacks that directly convert platform revenue into $ai16z buybacks.

This document outlines both the current tokenomic mechanics and the roadmap toward full activation of our tokenomics architecture.

## Current Mechanics

1. Primary SOL:AT Pools

When an Agent Token (AT) graduates from

[auto.fun](https://auto.fun/)

to Raydium, the liquidity is burned to create a Liquidity NFT, which is then paired with SOL in the primary liquidity pool (SOL:AT) on Raydium. This offers:

- Frictionless UX for traders: All interactions are SOL-denominated
- Deep initial liquidity: Ensured via highly-calibrated launch parameters
- Seamless integration with existing Solana DEX infrastructure

2. Liquidity NFTs for Project Creators (Powered by Raydium)

Each project that launches on

[auto.fun](https://auto.fun/)

is issued a Raydium Liquidity NFT, which represents ownership of future fees earned from the burned liquidity on Raydium. This innovation enables:

- Ongoing fee capture: Projects earn fees on every swap of their token
- Incentive alignment: Encourages builders to launch agents that provide sustained utility
- Permissionless ownership: These NFTs are composable and tradable across Solana

This structure flips the traditional launchpad model—empowering creators with onchain, revenue-generating assets rather than relying solely on dumping their token allocation or inflating their token supply via emissions.

3. Platform Fees → $ai16z Buybacks

10% of the burned LP (collected in SOL) is set aside when tokens graduate, and the fees generated are used to buyback $ai16z from the open market.

This forms a direct link between platform activity and value accrual for $ai16z holders—without imposing any friction on creators or users.

## Future Phase: Secondary Pools ( $ai16z:AT)

The next major evolution in our architecture introduces a secondary layer of liquidity: the $ai16z:AT pools. These are not yet live, but their function is defined and development will soon be underway.

When launched, these pools will:

- Pair acquired $ai16z (from buybacks) with the Agent Tokens whose fees generated the buysbacks
- This automatically deepens liquidity for both $ai16z and the successful ATs over their lifetime
- Deeper liquidity unlocks new routing paths, arbitrage opportunities, and composable LP dynamics. Creating a more sustainable and liquid $ai16z ecosystem

![Image](https://pbs.twimg.com/media/Go5bNx5XsAAlIIs?format=jpg&name=900x900)
(https://x.com/autodotfun/article/1915889326484766781/media/1913567183969431552)

These pools will be abstracted away from the user interface, operating as invisible infrastructure that enhances overall trading depth and price stability.

![Image](https://pbs.twimg.com/media/Go5bANPX0AALoiZ?format=jpg&name=900x900)
(https://x.com/autodotfun/article/1915889326484766781/media/1913566950791303168)

Future Flow Diagram (Under Development)

1. Fees collected in SOL and AT from SOL:AT pools
2. SOL → $ai16z buybacks via market operations
3. AT + $ai16z → $ai16z:AT liquidity provisioned
4. New pools integrated into routing for deeper ecosystem liquidity
5. Additional fees collected from secondary pools

These mechanics establish a compunding value loop: each trade strengthens the system, deepens liquidity, and feeds back into ecosystem growth.

## Design Principles

Our approach is built on core principles that balance modularity, alignment, and scalability:

- Minimize friction: Launching a new agent token should be as simple as possible
- Capture value organically: No forced staking, arbitrary burns, or hidden taxes
- Empower creators & community: Via liquidity ownership, fee share, and no-code agentic tools
- Evolve toward composability: Liquidity pools and routing logic are designed to integrate natively with Solana's DeFi stack

## The Long-Term Vision

The current phase of [auto.fun](https://auto.fun/) represents the foundation of a generative economic system. Agent developers gain tools and incentives to launch sustainably; traders benefit from deep liquidity and clean UX; and the broader Eliza ecosystem sees value compounded into $ai16z.

By partnering closely with Raydium, we've ensured that liquidity is not only deep, but modular—anchored in assets creators actually control, and governed by onchain primitives like NFTs and LP tokens. As we roll out the next phase of dual-pool mechanics, this foundation will allow for seamless compounding of fees, routing complexity, and capital efficiency—without introducing user friction.

In short: each new token strengthens the network. Each trade reinforces the system. And each builder becomes an owner in the economy they're helping create.
