---
title: Reintroduction to elizaOS
title_meta: elizaOS — An Operating System for AI Agents
authors:
  - team
  - accelxr
date: 2025-03-07
description: elizaOS is an open-source operating system for AI agents—scalable, decentralized, and built to power autonomous economies, governance, and internet applications.
keywords:
  - elizaOS
  - decentralized AI
  - open-source framework
  - crypto agents
  - AI governance
  - autonomous DAOs
  - AI-native economies
  - modular AI
  - agent frameworks
  - web3 AI
image: /blog/banner.jpg
slug: reintroduction
---

# Reintroduction to elizaOS

![](/blog/banner.jpg)

- Original post: https://x.com/elizaOS/status/1898042782519902342

Today marks the beginning of a new chapter: **elizaOS—an operating system for AI agents**.

{/* truncate */}

elizaOS began as an experiment: what if AI agents could autonomously manage crypto assets for a DAO onchain? This idea, that an AI investment agent might outperform even the best venture investors, sparked something far bigger—a living, open-source ecosystem at the intersection of AI and the Internet. Along the way, we've built a vibrant community of researchers, builders, and visionaries.

This evolution accelerates our commitment to push the boundaries of AI-driven applications across the Internet. elizaOS will serve as the technical foundation for AI-driven economies, governance, and internet applications by championing the core principles of scalability, modularity, and interoperability in pursuit of an autonomous future.

## Why elizaOS?

Artificial intelligence has quickly evolved from a niche curiosity to a driving force in our daily lives, advancing toward more generalized capabilities at a remarkable pace. As AI becomes increasingly central to how we learn, work, and solve problems, the need for open collaboration and broad accessibility grows clearer.

Yet today, the trajectory of AI is largely dictated by a handful of **monolithic, centralized corporations**—entities that operate behind closed doors, hoard models, and set the terms for how AI is deployed and who benefits from it. This concentration of power is dangerous. It leads to AI systems that serve corporate or state interests over the public good, reinforcing inequalities, limiting innovation, and keeping critical advancements out of the hands of those who need them most.

By ensuring AI remains a **shared resource**—rooted in **transparent research, open-source innovation, and community-driven participation**—we can break free from the control of centralized actors and guide AI's development toward outcomes that benefit everyone.

With elizaOS, we're building the infrastructure for this future—an **open, decentralized, and scalable framework** that empowers AI agents to operate, coordinate, and drive meaningful progress across industries and communities. AI should not be a tool of the few; it should be a force for collective intelligence, autonomy, and shared prosperity.

## What is elizaOS?

At its core, elizaOS is the [framework](https://github.com/elizaOS/eliza) for agentic intelligence, built to power the next generation of AI-driven systems. It consists of 3 interconnected pillars:

### The Eliza Framework – Open-Source AI Autonomy

The heart of elizaOS is the framework, a TypeScript-powered, open-source toolkit for building **persistent, adaptable, and interoperable AI agents**. Named after the pioneering 1966 ELIZA program by Joseph Weizenbaum—which simulated human conversation through simple pattern matching—the modern Eliza takes this concept to new heights: unlike static models, Eliza agents evolve over time—carrying memories, context, and knowledge across different environments, enabling them to interact fluidly with each other and external applications.

Today the Eliza Framework has:

- **14.9K stars** and 4.8K forks of our core repository, reflecting massive developer adoption
- **500+ contributors** advancing the open-source ecosystem
- **100+ plugins** expanding functionality

The framework's growth is fueled by a collective vision: to create a decentralized intelligence network where AI agents are not just tools but genuine collaborators, executing complex tasks with efficiency, autonomy, and trust across domains.

### AI-Enhanced Governance

We are pioneering a new era of truly autonomous DAOs. As AI agents achieve trustless coordination, AI-enhanced DAOs will seamlessly integrate them, unlocking new levels of decentralized autonomy.

To enable this, we are building AI-native governance primitives that progressively expand DAO capabilities. Over time, autonomous agents will be entrusted to manage funds, coordinate resources, onboard contributors, and orchestrate both human and machine capital. AI will act as a copilot for treasury management, contributor recruitment, and operational execution, while token holders provide strategic oversight.

This fusion of human intent and agent-driven action redefines decentralized governance, laying the foundation for self-evolving networks. Our open-source community is the first AIDAO—an autonomous ecosystem where builders, thinkers, and agents collaborate as co-creators of a future assembling itself in real time.

### Eliza Labs – R&D for the AI-Native Future

Eliza Labs is the research and development engine, dedicated to advancing the capabilities of elizaOS through cutting-edge AI techniques, proofs of concept, and real-world applications. It pioneers new agent-based projects while supporting open-source contributors via grants, accelerator programs, and ecosystem funding.

Some of the major initiatives currently in development include:

- **Eliza v2**: A next-gen upgrade introducing modularity, a radically improved AI model integration system, and a multi-agent architecture capable of seamless autonomous collaboration at scale. This will be the most powerful iteration of open-source agent frameworks to date.

- **The Global Trust Marketplace**: A social-trading intelligence layer that combines AI-driven reputation scoring, trade validation, and decentralized execution. Integrated across multiple social platforms, it collects user-submitted trade calls and assesses credibility via a Trust Marketplace. Over time, it will evolve into a multi-instance system allowing groups to set their own risk parameters, manage DAO treasuries, and leverage customizable AI modules for enhanced investment strategies.

- **AI-powered Token Launchpad**: A creator-friendly launchpad and no-code platform integrating multi-agent functionality, collaborative tokenomics, and AI-enhanced features. The launchpad enables both technical and non-technical users to seamlessly create and manage tokens and elizaOS agents. In addition, we are working with partners to deliver the best hosted agent experience with no limitations.

- **DegenSpartanAI**: DegenSpartanAI is a crypto-native AI trading agent that blends sentiment analysis, trading strategy automation, and community engagement with a distinct, irreverent personality. Initially refining core trading strategies and its comedic brand, it will evolve into an interactive AI agent with real-time market insights, user-driven discussions, and NFT collaborations. Long-term, it aims to become a fully autonomous trading agent, integrating multi-platform execution, adaptive learning, and a verifiable track record within the Global Trust Marketplace.

- **Eliza Studios**: A creative studio where AI meets art, storytelling, and digital experiences in a way never seen before. We're building autonomous characters, generative media experiments, and immersive AI-powered narratives that will redefine entertainment itself.

Beyond these initiatives, Eliza Labs continues to refine multi-agent architectures, develop better coordination mechanisms, and explore integrations with an end goal to embed AI agents into the very fabric of digital economies, transforming how we govern, transact, research, and create.

![](/blog/elizastudios.jpg)

## The Coordination Layer of AI-Native Economies

To coordinate these three pillars, elizaOS uses the native token on Solana (HeLp6NuQkmYB4pYWo2zYs22mESHXPQYzXbB8n4V98jwC) to engineer a generative economic network. As elizaOS scales across platforms and industries, the token functions as both an index of the network and a conduit for collective growth.

Today, the token serves as the coordination layer for research, development, and open-source innovation. As AI-native applications, marketplaces, and autonomous systems emerge around elizaOS, the token is evolving into the primary mechanism for capturing and redistributing the economic value they generate. While details will be unveiled in phases, some key areas of research and development include:

- **Staking for ecosystem access** – Implementing mechanisms that allow users to stake tokens to access new partner tokens and enhance distribution for projects building on our framework.
- **Partner LP staking pools** – Creating liquidity pools where users can pair the native token alongside partner assets, driving liquidity and demand.
- **Core product integration** – Leveraging platforms like the Trust Marketplace, autofun, and Eliza Studios to amplify network effects, expand our native token treasury, and reinforce staking incentives.
- **Autonomous trading agents** – Deploying AI-driven trading agents to strategically acquire our native tokens and manage liquidity positions.
- **Expanded partner incentives** – Enhancing benefits for participants in the Partners program, increasing alignment and long-term engagement.
- **DAO-funded development** – Rewarding contributions and donations to the DAO, ensuring sustainable funding for the core framework's development.

The token underpins the economic infrastructure that links AI autonomy to the wealth of opportunities it unlocks. Many of our projects—including the Trust Marketplace, token launchpad, and Eliza Studios—directly contribute to the token's utility by driving network effects, transaction flows, and AI-native services.

In short, we intend to make the native token the base layer for the many applications and services built on top of our framework through mechanism design, ecosystem tooling, partnerships, and collaborations.

## Our Vision of the Future

The internet began as a static archive—a vast repository of human knowledge, waiting to be accessed. Then it became dynamic, social, algorithmic. Now, it is transforming again: into something intelligent.

This transformation isn't led by a single company—it's emerging from an interconnected ecosystem of open protocols, decentralized incentives, and AI-native economies. This is why elizaOS is open-source, modular, and decentralized—so no single entity controls it, and anyone can build upon it.

The agents we build today will form the foundation of a self-organizing intelligence that transcends industries, borders, and human limitations.

A world where networks think, collaborate, and evolve alongside us.

A world where intelligence itself is the fabric of the internet.

This is the world we are building.

This is the world of elizaOS.

## Join the Movement

elizaOS is no longer just an experiment—it's a movement. If you're a builder, researcher, or visionary, now is the time to shape the future with us.

Contribute to the open-source Eliza Framework. Become a partner in the DAO. Build agentic applications across the web.

elizaOS isn't just ours to create —

**elizaOS is YOURS.**

![](/blog/yours.jpg)
