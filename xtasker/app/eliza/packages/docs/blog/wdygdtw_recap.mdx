---
slug: wdygdtw-recap
title: 'What Did You Get Done This Week? Recap'
description: A comprehensive archive of the weekly Twitter Spaces where developers shared their AI agent project progress
authors: jin
date: 2025-01-31
tags: [community, development, progress, twitter spaces, archive, video]
---

# "What Did You Get Done This Week?" Recap

From November 2024 to January 2025, we held a weekly Twitter Spaces where developers shared progress about their AI agent projects. If you shipped something related to open source AI / social agents that week, you were allowed 1-2 minutes to share an update of what you have been working on. Thousands of listeners tuned every Friday night to essentially listen to a dev standup.

{/* truncate */}

![](https://pbs.twimg.com/media/Fd2lvapVsAAFvsG.jpg)

The WDYGDTW series played a role in establishing momentum and maintaining it throughout the formative months as the crypto x AI agent space was heating up. The weekly accountability structure encouraged:

- **Rapid iteration cycles** from concept to implementation
- **Cross-pollination of ideas** between different teams
- **Public documentation** of the ecosystem's growth
- **Establishment of norms** around open development
- **Community reputation building** through consistent delivery

## Timeline

We transcribed, summarized, and uploaded notes for every Space shortly after each one. However, until now the recordings and notes haven't all been easily accessible all in one place. Showing up is half the battle. These are the pioneers of a grassroots crypto / AI movement.

```
- Nov 15, 2024
- 1: Social Agents / Advancing towards AGI
    - Logan, Kyle (Raid Guild), Glue, Ropey, Loaf, Odilitime, SomewheresHe, Robin, IQ6900, Marvin, Dot, JW, Neo, Bloom, Reality Spiral, Jen, OFI, Butoshi, <PERSON> (Geon <PERSON>born), HCP, <PERSON>, Lady Liberty, BoyaLockser, Amy, Griffin, <PERSON> (Heurist), Shaw, <PERSON>
    - Trust <PERSON>place, EVM wallet integration, Ducky AI client, Telegram fixes, Starknet wallet plugin, Sentience media generation, God's Fun, TEEs, Streamer platform, Decentralized AI cloud, Twitter client PR, Documentation, Satoshi AI memory system, Echo Chambers, Agent designs, Deep Writer, Music agent project, Psychic AI, Heurist API integration
        - https://www.youtube.com/watch?v=9EmvhlBPB8Q
- Nov 22, 2024
- 2: 3500 people tuning in to AI agent devs
    - Reality Spiral, Boyaloxer, Spaceodili, Yodamaster726, Wiki, Hashwarlock, KyleSt4rgarden, Nasdao_, Evepredict, ByornOeste, Empyrealdev, SkotiVi, YoungBalla1000x, SOL_CryptoGamer, Angelocass, DAOJonesPumpAI, RodrigoSotoAlt, CottenIO, HDPbilly, IQ6900, frankdegods, jamesyoung, 0xglu, chrislatorres, shannonNullCode, robotsreview, bcsmithx, JustJamieJoyce, yikesawjeez, HowieDuhzit, xrpublisher, BV_Bloom1, nftRanch, 019ec6e2, jacobmtucker, CurtisLaird5, unl__cky, Rowdymode, mitchcastanet, GoatOfGamblers, JohnNaulty, mayanicks0x, wakesync, TrenchBuddy, rakshitaphilip, MbBrainz, Hawkeye_Picks, Shaw, dankvr
    - GitHub integration, emotional plugin for agents, Reddit-based AI agent, Token Gods project, Coinbase plugin, AI agent streaming platform, DuckAI client, Hustle and Flow State, image generation prompts, Oasis agent simulation, Sentientopia, Eliza as "real girl", D-Gen Spartan revival, LaunchRate AI16DAO, Satoshi AI characters, MUSE DAO CEO, music-creating agent, livestreaming in 3D, log rotation tool, Earth Poker AI game, healthcare app, FXN swarm, Trust Marketplace, Eliza style guidelines, NFT project AI CEO, finance and entertainment projects, Twitter client with Vision AI
        - https://www.youtube.com/watch?v=Lbvv0Dr91Bc
- Nov 29, 2024
- 3: Community Building
    - Jin, Stargarden, Boya, Reality Spiral, W3Tester, HashWarlock, Soto, Mitch, Nick Parallel, Beige, Robin, Eve, Oguz, Swarm, RektDin, Roperito, Lothbrok, Clark Riswold, Tim, Spaceodili, Hawkeye, EA, FilteredThought, Yikes, Alain, Glue, Maximilian, Danny, Shaw, YoungJazzeth, Sergio
    - Self-sustaining AI DAOs, IRC connector, Dark Sun project, Binary solar system, 3D environments, BlockRat AI, Apollo health agent, data grading, Eliza interface, AI hosting platform, Rogue Agent podcast, "Life Engine", AI for logistics, Positivity AI, Eliza's World, Scriptoshi on Bitcoin, Marble auctions, Twitter integration, Web3 research, multi-agent system for code, quantum randomness, agent show marketing
        - https://www.youtube.com/watch?v=nUAEQ7uKi04
- Dec 6, 2024
- 4: Communications, Updates and Accountability
    - Shaw, Ropirito, Liam Zebedee, LordOfAFew, Robin, Reality Spiral, Ico, Glue, Shannon NullCode, JW, Guha, Frago, Yeshua God, AvaDoesAI, Spaceodili, Bloom, Joven, Satoshi_AI_Live, Simeon Fluck, NavarroCol, BeigeGrape, Andrew Miller, Johnny, Collins, Baron, Anthony, Wit, Ophi, Colin, Ranch, Oguz, Issy, Nick, Dr. K, BallerHash, Tim
    - Twitter & TikTok capabilities, Average French AI agent, Eliza framework improvements, Token Gods launch, Coinbase plugin, AI agent streaming platform, DuckAI client, knowledge graph system, LLM image prompts, Oasis simulation project, agent network connector, Sentientopia digital nation, Eliza as "real girl" concept, D-Gen Spartan revival, tokenomics, LaunchRate AI16DAO, small town AI characters, MUSE AI CEO, music-creating agent, market data livestreaming, log rotation security tool, Earth Poker AI game, crypto assistant, healthcare app, FXN swarm, Trust Marketplace, style guidelines, Tweek Labs AI CEO, finance projects, Baba Cat project, animation, KWAI network controller
        - https://www.youtube.com/watch?v=r3Z4lvu_ic4
- Dec 13, 2024
- 5: Building the Future
    - shawmakesmagic, xsubtropic, CottenIO, HDPbilly, IQ6900, frankdegods, jamesyoung, 0xglu, chrislatorres, reality_spiral, robotsreview, shannonNullCode, bcsmithx, boyaloxer, JustJamieJoyce, yikesawjeez, RodrigoSotoAlt, HowieDuhzit, xrpublisher, BV_Bloom1, nftRanch, 019ec6e2, jacobmtucker, CurtisLaird5, unl__cky, Rowdymode, mitchcastanet, GoatOfGamblers, JohnNaulty, mayanicks0x, wakesync, TrenchBuddy, rakshitaphilip, MbBrainz, Hawkeye_Picks, dankvr
    - Redux project, DaVinci AI, AI Summit recap, "Sploot" agent, on-chain ASCII art, character sheet tweaks, AI agent starter kit, agent swarms, Eliza.gg documentation, GitHub integration, Story Protocol plugin, Emblem Vault, Agent Tank, Plugin Feel for emotions, research AI agents, Discord bot, Metaplex NFTs, character generator, XR Publisher 3D network, 3D agent interactions, trading bot, Mimetic platform, agent transaction protocol, C-Studio interface, Escapism art generation, interactive streaming, binary star research, prediction market, SWE contributions, Axie AI KOL agent, Eliza Wakes Up, AWS templates, Brunette token, menu recommendations, storytelling bot
        - https://www.youtube.com/watch?v=4u8rbjmvWC0
- Dec 20, 2024
- 6: Hackathons, Frameworks, and the Race to Ship
    - dankvr, shawmakesmagic, IQ6900, spaceodili, bcsmithx, 0xBuildInPublic, jamesyoung, yikesawjeez, evepredict, yashhsm, TheLDAIntern, _0xaryan, CogAccSOL, reality_spiral, HDPbilly, CheddarQueso3D, ineedtendies, marvin_tong, BV_Bloom1, RealJonahBlake, DustinStockton, dylanpaulwhite, chrislatorres, 0xnavkumar, Hawkeye_Picks, lostgirldev, HowieDuhzit, boyaloxer, nizhanxi, ropirito, gigawidearray, GoatOfGamblers, shakkernerd, triadfi, MoondogFeed, wakesync, Moonbear, PoodonkAI, ViralMindAI, FilteredThought, _AnonDev, get_palet, MurrLincoln, socrates1024, IGLIVISION, dooly_dev, codergf_xyz, Ru7Longcrypto, sunosuporno, Signalman23, swarmnode, svabhishek, elohprojects, deltavius
    - Solana blockchain data service, GitHub PR merging, Agent Tank viral video, security auditing, AI agent hackathon, Matrix bridge, trading bot draft, Solana Agent Kit, market insights agent, website launch, GitHub client, tone control in agents, WSL setup guide, WordPress client, self-sustaining AI ecosystem, payment processing, 3D rigging, health agents, Sober Rover companion, Eliza.gg Q&A platform, Bitcoin runes project, Santa Pimp Claus token, SolEng agent, character generator updates, boredom tracking, Asia trip organizing, AWS hosting, Reddit plugin, prediction market for memecoins, Redis caching, AI personality agents, social media to meme coins, persistent memory, vvaifu agent, AI hive mind, decentralized training platform, auto-trading agent, "Mizuki" AI model, open context protocol, AgentKit updates, TEE login system, game framework study, AGI matching platform, one-click chatbot deployment
        - https://www.youtube.com/watch?v=R3auUQj9oEg
- Dec 27, 2024
- 7: Agentic Documentation and GitHub Integration
    - ai16zdao, shawmakesmagic, spaceodili, 0xBuildingPublic, Im_zo_eth, IQ6900_, FilteredThought, yeahimomar, affaanmustafa, KyleSt4rgarden, SYMBiEX, codergf_xyz, GoatOfGamblers, SuperfruitsAi, hashwarlock, allenharper, witconomist, triadfi, human_for_now, reality_spiral, lordOfAFew, chrislatorres, evepredict, lostgirldev, r4dicalcentrism, 0xblacksun, tmoindustries, wakesync, sunosuporno, unl__cky, hotpot_intern, TrenchBuddy, Signalman23, thelotioncoin, anshikag85, Doc_strange1, dankvr
    - Hyperfy multi-agent integration, agentic JS documentation, on-chain agent "Q", data storage cost reduction, trading system with TrustDB, Minecraft AI villagers, streaming coding sessions, Solana AI hackathon, character creation tool, Web app staging, prediction market, web3 security agents, Spore agent swarm, "Shaw" character file, Trust marketplace white paper, prediction market analyst, search engine for dev videos, GitHub adapter, recursive self-improvement, generative agents for on-chain games, V2 development meetings, travel influencer agent, PR review agents, SoulScript for agent personalities, digital archaeologist agent, climate/nature focused ERC6551 agents, Eliza Wakes Up web app, DeFi agent, autonomous audio/song generation, TikTok memecoin hunter, wallet tracking visualization, voice AI for Twitter Spaces, integrating AI into existing projects, AI/AWS newsletter, TikTok integration
        - https://www.youtube.com/watch?v=jcSF7dSicTI
- Jan 3, 2025
- 8: From DeFi to Social Media
    - ai16zdao, shawmakesmagic, astridhpilla, lostgirldev, spaceodili, 0xBuildInPublic, youfadedwealth, nftRanch, SYMBiEX, SuperfruitsAi, TimshelXYZ, chrislatorres, AIFlow_ML, jamesyoung, deadlock_1991, yeahimomar, human_for_now, lordasado, RodrigoSotoAlt, HDPbilly, GoatOfGamblers, Titan_Node, KyleSt4rgarden, unl__cky, CheddarQueso3D, sunosuporno, tmoindustries, Sawyer_APRO, wakesync, Ru7Longcrypto, marko_post, NEETOCRACY, HefAiGent, reality_spiral, witconomist, triadfi, Rowdymode, MaushishYadav, chaininsured, godfreymeyer, thelotioncoin, codergf_xyz, IGLIVISION, EledraNguyen, GnonOnSolana, Satoshi_BTCFi, swarmnode, memeillionaire, krauscrypto, usebuildfun, affaanmustafa, O_on_X, AITATsol, xiao_zcloak, Protocol_Blend, yq_acc, akshayynft, BenjiStackzzz, 0xBuns, aiquantfun
    - Miku chatbot relaunch, Selene growth & PR review, Eliza framework fixes, voice features, plugin isolation, Audits agent documentation, PP coin automated trading, framework integration, DeepSeek model provider, Dragon Fruit AI launch, Meetup Fund platform, Eliza partnerships, knowledge graph for repos, verifiable inference system, Alice AI fund management, Pixocracy AI village management, form fill infrastructure, Smol World agent reasoning, Bosu memory management, Twitter client reflection loop, Goat Arena prediction market, LivePeer inference endpoints, Solana token staking, media generation improvements, agent documentation, DeFi assistant waitlist, region swarm voice integration, BNB chain integration, Netflix & chill extension, dating coach AI agent, Mars' first digital citizen, Army of Indians DAO, ERC 314 technology integration, GitHub client for scrum planning, Marketplace of Trust white paper, AI personality expansion, Twin Tone testing, yield optimizing agent, insurance broker agent, 3D news show avatars, AI agents for social channels, Haruka Twitter bot, NFT marketplace on Superchain, Square Fun AI analytics, Echo Chambers v2.3, Swarm Node growth, token integration, voice cloning mobile app, no-code AI agent builder, project scaling strategies, AI agent unsuspension techniques, global trade analysis, crypto payment functionality, DeFi protocol user experience
        - https://www.youtube.com/watch?v=Vs7D5DN_trk
- Jan 10, 2025
- 9: AI Agents to DePIN
    - ai16zdao, spaceodili, 0xBuildInPublic, yeahimomar, unl__cky, CheddarQueso3D, lostgirldev, ohhshiny, SYMBiEX, nftRanch, HDPbilly, zerokn0wledge_, KingBootoshi, calintje, hashwarlock, MattPRD, dreygo_, 0xShiroe, lostboydev, brownsvgar, human_for_now, aiagentpepe, sea_of_zhou, tito_cda, thelotioncoin, chineseremilio, _cjft, dino2deno, AIFlow_ML, tmoindustries, astridhpilla, marvin_tong, yikesawjeez, djsamforever, KyleSt4rgarden, ProfRizzAI, vargs_g, KarimaDigital, Amiewitheliza, reality_spiral, wenkafka, slmsolcto, AaronErickson, GoatOfGamblers, c0mput3rxz, wakesync, aiquantfun, sunosuporno, ongo_ai, y7_y00ts, xiao_zcloak, ViralMindAI, Artstridee, bryanjmonterrey, O_on_X, svabhishek, CottenIO, hotpot_intern, TimshelXYZ, shawmakesmagic, dankvr
    - Database and memory systems, documentation enhancements, Pixocracy Launchpad, image and music generation, AI in education, Aora project, hackathon judging, troll bot agent, TEE exploration, Discord deployment, DeFi agent swarm, summary kernel experiment, on-chain swaps, cross-VM bridging, Feather agent framework, Orca liquidity provisioning, Oracle agent on Sporephone, research paper auditing, market-making platform, GigaBread jailbreak testing, Solimp realism enhancement, Eliza texting buddy, automatic data collection, DePIN plugin for real-world data, Dark Sun digital investigator, platform front-end implementation, AI model Zion, AWS dockerization, multi-wallet volume bot, Akash plugin development, insurance app for natural capital, CES meetings and partnerships, TEE Cloud onboarding, Eliza PR merging system, Shogun plugin contributions, token staking documentation, Riz.ai entertainment platform, Zero-G storage plugin, dating coach AI, ecosystem integration, prompt logging system, wallet natural language commands, time series modeling for hurricanes, Goat Arena platform, token selector plugin, "simp to earn" feature, AI quant launchpad, Midas project wallet infrastructure, Ongo art critic expansion, Utes sports analytics, TikTok wallet integration, Minecraft tournament infrastructure, trading dashboard with social features, X account suspension workarounds, RAP framework development, image generation training, ZoroX TikTok coin hunter
        - https://www.youtube.com/watch?v=fqM_vYK2bmc
- Jan 17, 2025
- 10: From Digital to Physical
    - ai16zdao, shawmakesmagic, JustinMoonAI, AntiRugAgent, rckprtr, dreygo_, Audix_hq, coordinape, lostgirldev, AIFlow_ML, astridhpilla, thelotioncoin, RodrigoSotoAlt, berliangor, unl__cky, xiao_zcloak, 0xnavkumar, GoatOfGamblers, Amiewitheliza, 0xVEER, BuzzyCrypto_, SYMBiEX, w1kke, luki_notlowkey, AgentTextdotfun, yikesawjeez, ByornOeste, Nasdao_, elizawakesup, dankvr, sypherlit
    -  Eliza V2 development, inventory system, CLI, Tron integration, rug pull prevention, Discover AI community management, Kyra AI market-making, smart contract visuals, Farcaster community rewards, website and terminal development, PR agent for ElizaOS, voice and VRM development, API connections, persistent memory system, Pglite rewriting in Rust, Telegram launch, "Approve Agents" model for wallets, verifiable TEE Network, Telegram mini apps, internal team calls, ETH Denver AI program, autonomous trader and music releases, web search in agents, devotion program staking, on-chain data plugin, DePIN network for SMS, CICD improvements, conspiracy theory thesis, DeFi validator personality, Eliza's robot body, Degen Spartan AI trading, Void AI cross-chain mixer
    - https://www.youtube.com/watch?v=1voboZEQTAw
- Jan 24, 2025
- 11: AI Agents Level Up
    - ai16zdao, SYMBiEX, astridhpilla, davidlsneider, dreygo_, GoatOfGamblers, unl__cky, thelotioncoin, Amiewitheliza, lostboydev, lostgirldev, AIFlow_ML, _AnonDev, damascoai, ITzMiZzle, MementsOfficial, immanencer, MrMinter_eth, FilteredThought, AgienceAI, BotOrNot42, itsmetamike, sea_of_zhou, TimshelXYZ, wakesync, reality_spiral, ai16zdao, yikesawjeez
    - DeepSeek R1 integration, Fleek partnership, Lit Protocol Agent Wallet Kit, Ninja Terminal market making, GoToArena Telegram bot, X image generation, agent platform MVP, Trust Marketplace paper, Telegram mod functionality, Solenguration B2B terminal, Hyperbolic agent tracking, cybersecurity ecosystem, AI security framework, Kiyomi AI voice and music, SQL Agents library, AI agent swarms with D&D stats, agent business logic, Suno/Udio plugin, open source agent platform token, Vice agent content creation, Hyperfy tests, Quicksilver prediction framework, Eliza email scheduling, Simp2Earn tokenomics, GitHub UI relationship module, improved news aggregator, ElizaOS CICD improvements
        - https://www.youtube.com/watch?v=mxdWvBnxN8M
- Jan 31, 2025
- 12: ElizaOS 0.1.9 Launch
    - ai16zdao, shawmakesmagic, astridhpilla, lostgirldev, xrpublisher, lostboydev, spaceodili, SYMBiEX, yikesawjeez, Amiewitheliza, 0xrhota, ai16zdao, wakesync, AIFlow_ML, Signalman23, Rowdymode, MementsOfficial, elizawakesup, reality_spiral, tmoindustries, w1kke, shawmakesmagic
    - DUNA Framework for DAOs, Miku updates (MetaHuman, ETHDenver), SolEng Terminal launch, Pixel memory system with backups, SolImp Telegram mod platform, ElizaOS v0.1.9 release with plugin registry, website rework with 3D models, ecosystem updates (tokenomics, self-hosting), HyperPoly plugin and marketplace integration, Degen Spartan trading enhancements, Block Tank show format, Eliza voice device development, Backpack plugin and typing fixes, voice agent model fine-tuning, Gods Unchained tokenomics, AI database interaction library, voice demo with improved latency, Coinbase grant and trading on Base, insurance agents for nature and biodiversity, Gods Unchained plugins and Devotion program
        - https://www.youtube.com/watch?v=SZNuoXJ1Mvs
```

## Video Archive for Research

These recordings help document the week-by-week evolution of the crypto AI ecosystem, capturing the iterative development process and community formation in real-time. NotebookLM and similar research tools can extract specific technical discussions, discover new connections, and track project evolution through these as sources.

Below is a complete list of all WDYGDTW session recordings. These videos can be imported into tools like Google's [NotebookLM](https://notebooklm.google.com/) via youtube video import for deeper analysis, transcript generation, and pattern recognition across the development timeline:

[![image](/blog/notebooklm.jpg)](https://notebooklm.google.com/)

**Youtube**

1. https://www.youtube.com/watch?v=9EmvhlBPB8Q
2. https://www.youtube.com/watch?v=Lbvv0Dr91Bc
3. https://www.youtube.com/watch?v=nUAEQ7uKi04
4. https://www.youtube.com/watch?v=r3Z4lvu_ic4
5. https://www.youtube.com/watch?v=4u8rbjmvWC0
6. https://www.youtube.com/watch?v=R3auUQj9oEg
7. https://www.youtube.com/watch?v=jcSF7dSicTI
8. https://www.youtube.com/watch?v=Vs7D5DN_trk
9. https://www.youtube.com/watch?v=fqM_vYK2bmc
10. https://www.youtube.com/watch?v=1voboZEQTAw
11. https://www.youtube.com/watch?v=mxdWvBnxN8M
12. https://www.youtube.com/watch?v=SZNuoXJ1Mvs

**Notes**

1. [WDYGDTW #1](/community/streams/11-2024/2024-11-15)
2. [WDYGDTW #2](/community/streams/11-2024/2024-11-22)
3. [WDYGDTW #3](/community/streams/11-2024/2024-11-29)
4. [WDYGDTW #4](/community/streams/12-2024/2024-12-06)
5. [WDYGDTW #5](/community/streams/12-2024/2024-12-13)
6. [WDYGDTW #6](/community/streams/12-2024/2024-12-20)
7. [WDYGDTW #7](/community/streams/12-2024/2024-12-27)
8. [WDYGDTW #8](/community/streams/01-2025/2025-01-03)
9. [WDYGDTW #9](/community/streams/01-2025/2025-01-10)
10. [WDYGDTW #10](/community/streams/01-2025/2025-01-17)
11. [WDYGDTW #11](/community/streams/01-2025/2025-01-24)
12. [WDYGDTW #12](/community/streams/01-2025/2025-01-31)

If you do something cool with this data, let us know in the [discord](discord.gg/ai16z)!

After 3 straight months of nonstop building, it was time for a rest period. We may bring this format back, but we also want to continue the spirit of public accountability and transparent development through other community initiatives to keep things fresh. One such example is [Clank Tank](https://m3org.com/tv), where a standup becomes a pitch to AI judges that give you feedback about your project.
