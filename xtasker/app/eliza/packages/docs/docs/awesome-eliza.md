---
hide_title: true
title: Awesome ElizaOS
description: A curated list of awesome ElizaOS resources, plugins, tools, and integrations
keywords:
  [
    awesome,
    resources,
    plugins,
    tools,
    integrations,
    clients,
    adapters,
    blockchain,
    DeFi,
    AI,
    social,
    infrastructure,
  ]
image: /img/banner.png
sidebar_position: 6
---

# Awesome elizaOS

A curated list of awesome things related to the [eliza framework](https://github.com/elizaOS/eliza).

<p align="center">                                                                                                     
  <img src="https://raw.githubusercontent.com/elizaos/awesome-eliza/refs/heads/main/assets/banner.png" alt="elizaOS Banner" />
</p>

<p align="center">
  <a href="https://elizaos.ai">Website</a>
  •
  <a href="https://github.com/elizaOS/eliza">GitHub</a>
  •
  <a href="https://x.com/elizaos">Twitter</a>
  •
  <a href="https://discord.gg/elizaos">Discord</a>
  •
  <a href="https://t.me/elizaOSdev">Telegram</a>
</p>

---

## 📋 Table of Contents

- [🏢 Official Developer Resources](#official-developer-resources)
- [📚 Tutorials and Learning Resources](#tutorials-and-learning-resources)
- [🛠️ Tools](#tools)
- [🔌 Integrations](#integrations)
- [🔄 Clients](#clients)
- [💾 Adapters](#adapters)
- [🧩 Plugins](#plugins)
  - [⛓️ Blockchain & DeFi](#blockchain--defi)
  - [📈 Crypto Trading & Exchanges](#crypto-trading--exchanges)
  - [🧠 AI & Data](#ai--data)
  - [🎨 Media & Content](#media--content)
  - [💬 Social & Communication](#social--communication)
  - [🔒 Infrastructure & Security](#infrastructure--security)
  - [🔧 Tools & Utilities](#tools--utilities)
- [👥 Community](#community)
- [🎬 Videos and Spaces](#videos-and-spaces)
  - [📅 What Did You Get Done This Week?](#what-did-you-get-done-this-week)
- [📊 Research and News](#research-and-news)
- [👏 Contributors](#contributors)

---

## 🏢 Official Developer Resources

- [elizaOS GitHub Repository](https://github.com/elizaos/eliza) - The main repository for elizaOS, an open-source AI agent framework.
- [elizaOS Documentation](https://elizaos.github.io/eliza/) - Official documentation and guides
- [Sample Characters](https://github.com/elizaos/characters) - Sample character templates and implementations
- [elizaOS Plugin Registry](https://github.com/elizaos-plugins/registry) - JSON Registry for all the plugins in the elizaOS ecosystem
- [Contributor Leaderboard](https://elizaos.github.io) - See all the contributors for elizaos/eliza, view daily GitHub summaries

## 📚 Tutorials and Learning Resources

- [Quickstart Guide](https://elizaos.github.io/eliza/quickstart) - Get up and running quickly with elizaOS
- [Building a Social AI Agent in 15 Minutes](https://www.youtube.com/embed/6PZVwNTl5hI?si=0zB3OvYU4KiRQTxI) - Nader's Tutorial
- [Creating an AI Agent with Your Own Personality](https://www.youtube.com/embed/uouSdtcWXTQ?si=cm13L4T7DQUMXd0C) - Nader's Tutorial
- [How to Build an API Plugin](https://www.youtube.com/embed/25FxjscBHuo) - Nader's Tutorial ([Code](https://github.com/dabit3/eliza-nasa-plugin))
- [AI Agent Dev School 01](https://www.youtube.com/watch?v=ArptLpQiKfI) - Introduction and Foundations (Part 1) [CN](https://www.youtube.com/watch?v=0CB_u6J9_Bo)
- [AI Agent Dev School 01 cont](https://www.youtube.com/watch?v=AC3h_KzLARo) - Introduction and Foundations (Part 1 cont)
- [AI Agent Dev School 02](https://www.youtube.com/watch?v=X1aFEOaGcYE) - Deep Dive into Actions, Providers, and Evaluators (Standard Definition)
- [AI Agent Dev School 03](https://www.youtube.com/watch?v=Y1DiqSVy4aU) - Building a User Data Extraction Agent
- [AI Agent Dev School 04](https://www.youtube.com/watch?v=6I9e9pJprDI) - AI Pizza: Hacking Eliza for Domino's Delivery (plus TEE Deep Dive)

## 🛠️ Tools

- [elizagen](https://elizagen.howieduhzit.best/) - Easy eliza characterfile generator, add your own API keys
- [Fleek.xyz](https://fleek.xyz/eliza/) - Deploy Eliza agents in one click on a hosted platform

## 🔌 Integrations

- [Hyperfy](https://hyperfy.how/guides/ai/agents/) - agent creation, autonomous behavior, NPC development in open source 3d web
- [Nifty Island](https://docs.niftyisland.com/creator/agents/tutorials/eliza/) - Deploy elizas into Nifty Island and beyond

---

## 🔄 Clients

- [Discord](https://github.com/elizaos-plugins/client-discord) - Discord integration with messaging and community interaction capabilities
- [Farcaster](https://github.com/elizaos-plugins/client-farcaster) - Client for Farcaster decentralized social media protocol
- [GitHub](https://github.com/elizaos-plugins/client-github) - GitHub client for repository management and code integration
- [Lens](https://github.com/elizaos-plugins/client-lens) - Client for Lens Protocol decentralized social networking
- [Slack](https://github.com/elizaos-plugins/client-slack) - Slack integration with messaging and workspace collaboration features
- [Telegram](https://github.com/elizaos-plugins/client-telegram) - Telegram messaging client with bot and channel integration capabilities
- [Tako](https://github.com/takoprotocol/client-tako) - Client for the Tako protocol enabling cross-platform social engagement
- [Alexa](https://github.com/elizaos-plugins/client-alexa) - Integration with Amazon Alexa devices for voice-controlled agent interactions
- [Deva](https://github.com/elizaos-plugins/client-deva) - Communication with Deva AI systems for advanced conversational capabilities
- [Eliza Home](https://github.com/elizaos-plugins/client-eliza-home) - Integration with Eliza Home environment for home automation
- [Instagram](https://github.com/elizaos-plugins/client-instagram) - Instagram integration with support for media posting and interactions
- [Telegram Account](https://github.com/elizaos-plugins/client-telegram-account) - Advanced Telegram account management beyond basic bot functionality
- [XMTP](https://github.com/elizaos-plugins/client-xmtp) - Secure, decentralized, and end-to-end encrypted messaging through XMTP protocol

## 💾 Adapters

- [MongoDB](https://github.com/elizaos-plugins/adapter-mongodb) - Database adapter for MongoDB enabling document storage with query capabilities
- [Postgres](https://github.com/elizaos-plugins/adapter-postgres) - Database adapter for PostgreSQL with full relational database capabilities
- [Sqlite](https://github.com/elizaos-plugins/adapter-sqlite) - Lightweight PostgreSQL adapter for embedded database applications
- [Qdrant](https://github.com/elizaos-plugins/adapter-qdrant) - Vector database adapter for Qdrant with similarity search capabilities
- [SQLite](https://github.com/elizaos-plugins/adapter-sqlite) - File-based SQL database adapter for lightweight local storage
- [Supabase](https://github.com/elizaos-plugins/adapter-supabase) - Database adapter for Supabase with PostgreSQL and realtime capabilities

## 🧩 Plugins

### ⛓️ Blockchain & DeFi

- [0x](https://github.com/elizaos-plugins/plugin-0x) - Token swaps through 0x Protocol across multiple EVM blockchains
- [Aptos](https://github.com/elizaos-plugins/plugin-aptos) - Token transfers and wallet management on the Aptos blockchain
- [Arthera](https://github.com/elizaos-plugins/plugin-arthera) - Transactions and token operations on the Arthera network
- [Avalanche](https://github.com/elizaos-plugins/plugin-avalanche) - DeFi operations and token management on Avalanche blockchain
- [BNB](https://github.com/elizaos-plugins/plugin-bnb) - Interactions with BNB Chain ecosystem including BNB Smart Chain
- [Cosmos](https://github.com/elizaos-plugins/plugin-cosmos) - Token management and transfers using Cosmos-compatible blockchains
- [Cronos](https://github.com/elizaos-plugins/plugin-cronos) - EVM plugin functionality for Cronos blockchain with native CRO token support
- [CronosZKEVM](https://github.com/elizaos-plugins/plugin-cronoszkevm) - Token transfers and interactions on the Cronos zkEVM network
- [EVM](https://github.com/elizaos-plugins/plugin-evm) - Comprehensive functionality for interacting with EVM-compatible chains
- [Flow](https://github.com/fixes-world/plugin-flow) - Flow blockchain interactions for token transfers and smart contracts
- [Flow Advanced](https://github.com/fixes-world/plugin-flow-advanced) - Advanced plugin for Flow Blockchain operations
- [Fuel](https://github.com/elizaos-plugins/plugin-fuel) - ETH transfers and interactions on the Fuel Ignition network
- [GOAT](https://github.com/elizaos-plugins/plugin-goat) - Blockchain capabilities through the Great Onchain Agent Toolkit
- [Hyperliquid](https://github.com/elizaos-plugins/plugin-hyperliquid) - Spot trading capabilities on the Hyperliquid DEX
- [ICP](https://github.com/elizaos-plugins/plugin-icp) - Internet Computer Protocol interactions for token operations
- [Lightlink](https://github.com/lightlink-network/plugin-lightlink) - Ethereum layer 2 operations on the Lightlink blockchain network
- [Massa](https://github.com/elizaos-plugins/plugin-massa) - Interactions with the Massa blockchain ecosystem
- [Mina](https://github.com/elizaos-plugins/plugin-mina) - Mina blockchain interactions for token transfers and wallet management
- [Movement](https://github.com/elizaos-plugins/plugin-movement) - Movement Network blockchain functionality for token operations
- [MultiversX](https://github.com/elizaos-plugins/plugin-multiversx) - Token operations and transfers on the MultiversX blockchain
- [NEAR](https://github.com/elizaos-plugins/plugin-near) - Token management and transfers on the NEAR Protocol blockchain
- [Sei](https://github.com/elizaos-plugins/plugin-sei) - Token transfers and operations on the Sei blockchain network
- [Solana](https://github.com/elizaos-plugins/plugin-solana) - Comprehensive DeFi operations on the Solana blockchain
- [Solana AgentKit](https://github.com/elizaos-plugins/plugin-solana-agentkit) - Interaction with Solana blockchain through AgentKit
- [Solana Agent Kit](https://github.com/elizaos-plugins/plugin-solana-agent-kit) - Token operations and NFT functionality on Solana
- [Solana V2](https://github.com/elizaos-plugins/plugin-solana-v2) - Modern Solana integrations with liquidity position management
- [Starknet](https://github.com/elizaos-plugins/plugin-starknet) - Token and DeFi operations on the Starknet blockchain
- [Sui](https://github.com/elizaos-plugins/plugin-sui) - Token transfers and wallet operations on the Sui blockchain
- [TON](https://github.com/elizaos-plugins/plugin-ton) - TON blockchain operations and wallet functionality
- [zkSync Era](https://github.com/elizaos-plugins/plugin-zksync-era) - Token transfers and operations on the zkSync Era network
- [Zilliqa](https://github.com/elizaos-plugins/plugin-zilliqa) - Zilliqa blockchain capabilities through the GOAT framework
- [Initia](https://github.com/elizaos-plugins/plugin-initia) - Token transfers on the Initia blockchain with INIT token support
- [Injective](https://github.com/elizaos-plugins/plugin-injective) - Interaction with the Injective blockchain through module-based actions
- [Abstract](https://github.com/elizaos-plugins/plugin-abstract) - Token transfers and operations on the Abstract blockchain network
- [Avail](https://github.com/elizaos-plugins/plugin-avail) - Interacts with Avail DA network for data availability and transfers
- [B2](https://github.com/elizaos-plugins/plugin-b2) - Token transfers on the B2-Network blockchain with B2-BTC support
- [Conflux](https://github.com/elizaos-plugins/plugin-conflux) - Token transfers and interactions on the Conflux blockchain network
- [Genlayer](https://github.com/elizaos-plugins/plugin-genlayer) - Contract deployment and interactions on the GenLayer protocol
- [Form](https://github.com/elizaos-plugins/plugin-form) - Form chain capabilities for curves-based token economics
- [Multichain](https://github.com/near-agent/elizaos-plugin-multichain) - Cross-chain interaction capabilities for multiple blockchain networks
- [Quai](https://github.com/elizaos-plugins/plugin-quai) - Token transfers and blockchain interactions on Quai Network
- [Omniflix](https://github.com/elizaos-plugins/plugin-omniflix) - Interactions with the OmniFlix Network blockchain
- [Viction](https://github.com/BuildOnViction/plugin-viction) - Token operations, trading, and DeFi integrations using Viction
- [Squid Router](https://github.com/elizaos-plugins/plugin-squid-router) - Cross-chain token swaps between blockchains using Squid Router
- [Router Nitro](https://github.com/elizaos-plugins/plugin-router-nitro) - Cross-chain token transfers using Router Nitro bridge
- [Rabbi Trader](https://github.com/elizaos-plugins/plugin-rabbi-trader) - Automated cryptocurrency trading on Solana with trust scoring
- [Trikon](https://github.com/elizaos-plugins/plugin-trikon) - Token transfers for Trikon with wallet management on ZKsync Era
- [AgentKit](https://github.com/elizaos-plugins/plugin-agentkit) - CDP AgentKit tools for NFT and token management on blockchains

### 📈 Crypto Trading & Exchanges

- [Binance](https://github.com/elizaos-plugins/plugin-binance) - Cryptocurrency trading and price checking via Binance API
- [CCXT](https://github.com/pranavjadhav1363/plugin-ccxt) - Cryptocurrency trading and arbitrage across multiple exchanges
- [Coinbase](https://github.com/elizaos-plugins/plugin-coinbase) - Integration with Coinbase's various APIs and trading services
- [CoinGecko](https://github.com/elizaos-plugins/plugin-coingecko) - Cryptocurrency price data and market information from CoinGecko
- [CoinMarketCap](https://github.com/elizaos-plugins/plugin-coinmarketcap) - Cryptocurrency price checking using CoinMarketCap's API
- [Compass](https://github.com/CompassLabs/plugin-compass) - Integration with Compass API for executing operations on DeFi protocols
- [Desk Exchange](https://github.com/elizaos-plugins/plugin-desk-exchange) - Integration with DESK Exchange for perpetual futures trading
- [Dexscreener](https://github.com/elizaos-plugins/plugin-dexscreener) - DexScreener's token data for price information and market trends
- [Birdeye](https://github.com/elizaos-plugins/plugin-birdeye) - DeFi and token analytics through Birdeye's API
- [Merkle](https://github.com/merkle-trade/merkle-eliza-plugin) - MerkleTrade platform for trading operations with API-based price tracking
- [OKX](https://github.com/elizaos-plugins/plugin-okx) - Trading and asset management through OKX exchange API
- [Holdstation](https://github.com/elizaos-plugins/plugin-holdstation) - Token swapping on Holdstation DEX with ZKsync Era support
- [Arbitrage](https://github.com/elizaos-plugins/plugin-arbitrage) - Identifies and executes cryptocurrency arbitrage opportunities
- [Grix](https://github.com/grixprotocol/plugin-grix) - DeFi options data fetching and price analysis through Grix Finance API
- [Pyth Data](https://github.com/elizaos-plugins/plugin-pyth-data) - Real-time price feeds and data streams across multiple asset classes
- [Zapper](https://github.com/ben-dh3/plugin-zapper) - Portfolio tracking and DeFi position management across multiple chains
- [Zerion](https://github.com/elizaos-plugins/plugin-zerion) - Wallet portfolio and position data using the Zerion API
- [FerePro](https://github.com/elizaos-plugins/plugin-ferePro) - WebSocket communication with FerePro API for market insights
- [Messari AI Toolkit](https://github.com/messari/plugin-messari-ai-toolkit) - Crypto market research capabilities using Messari's AI Toolkit
- [Moralis](https://github.com/elizaos-plugins/plugin-moralis) - Real-time DeFi data including trading pairs and price history
- [Ankr](https://github.com/elizaos-plugins/plugin-ankr) - Blockchain data queries for wallet information and token analytics
- [Chainbase](https://github.com/elizaos-plugins/plugin-chainbase) - Natural language interactions with blockchain data across networks
- [Edwin](https://github.com/elizaos-plugins/plugin-edwin) - Interaction with Edwin tools for DeFi operations
- [NFT Collections](https://github.com/elizaos-plugins/plugin-nft-collections) - NFT data services for market analytics with 420+ verified collections

### 🧠 AI & Data

- [Allora](https://github.com/elizaos-plugins/plugin-allora) - Real-time AI inferences from Allora Network for market predictions
- [Asterai](https://github.com/elizaos-plugins/plugin-asterai) - Integration with asterai.io plugins and agents for enhanced AI capabilities
- [ATTPs](https://github.com/APRO-com/plugin-ATTPs) - Verification of agent activities using proof generation and validation
- [Autonome](https://github.com/elizaos-plugins/plugin-autonome) - Launches and manages new Eliza agents through the Autonome platform
- [Bittensor](https://github.com/elizaos-plugins/plugin-bittensor) - Integration with BitMind's API for accessing AI services on Bittensor
- [Devin](https://github.com/elizaos-plugins/plugin-devin) - Integration with Devin API for automated engineering assistance
- [Isaacx](https://github.com/isaacx0/plugin-isaacx) - Advanced AI reasoning and cognitive modeling plugin
- [Mind Network](https://github.com/elizaos-plugins/plugin-mind-network) - Integration with Mind Network Hubs for secure, privacy-preserving voting
- [NVIDIA NIM](https://github.com/elizaos-plugins/plugin-nvidia-nim) - NVIDIA's AI foundation models for content analysis and safety checks
- [OpenAI](https://github.com/elizaos-plugins/plugin-openai) - Integration with OpenAI's GPT models for automated text generation
- [Galadriel](https://github.com/elizaos-plugins/plugin-galadriel) - Verified inference API for on-chain agent verification
- [LLaMA](https://github.com/elizaos-plugins/plugin-llama) - Local LLM capabilities using LLaMA models with CPU and GPU support
- [D.a.t.a](https://github.com/carv-protocol/plugin-d.a.t.a) - Data processing with authentication and trust scoring
- [AlphaNeural](https://github.com/alphaneuralai/plugin-alphaneural) - Neural network capabilities for AI agents

### 🎨 Media & Content

- [3D Generation](https://github.com/elizaos-plugins/plugin-3d-generation) - Generates 3D models from text descriptions using FAL.ai
- [Image](https://github.com/elizaos-plugins/plugin-image) - Processing and analysis of images with multiple vision model providers
- [Image Generation](https://github.com/elizaos-plugins/plugin-image-generation) - Generates and manages images with storage and optimization features
- [Letzai](https://github.com/elizaos-plugins/plugin-letzai) - Image generation using LetzAI's API and models
- [NFT Generation](https://github.com/elizaos-plugins/plugin-nft-generation) - Creates NFT collections with AI-generated artwork on Solana
- [Video](https://github.com/elizaos-plugins/plugin-video) - Comprehensive video processing with download and transcription
- [Video Generation](https://github.com/elizaos-plugins/plugin-video-generation) - Generates videos using Luma AI's API services
- [Story](https://github.com/elizaos-plugins/plugin-story) - Generates interactive storytelling experiences with branching narratives
- [Suno](https://github.com/elizaos-plugins/plugin-suno) - Integration with Suno AI's music generation from text prompts
- [UDIO](https://github.com/elizaos-plugins/plugin-udio) - AI-powered music generation and extension from text prompts
- [Giphy](https://github.com/elizaos-plugins/plugin-giphy) - Sending GIFs in response to user messages using the Giphy API
- [imgflip](https://github.com/elizaos-plugins/plugin-imgflip) - Generates memes using the imgflip.com API based on user messages
- [TTS](https://github.com/elizaos-plugins/plugin-tts) - Generates speech from text using FAL.ai's API
- [Speech TTS](https://github.com/elizaos-plugins/plugin-speech-tts) - Text-to-speech transcription using OpenAI and ElevenLabs
- [Para](https://github.com/aipop-fun/plugin-para) - NFT and digital collectible management platform integration
- [YouTube to Text](https://github.com/wellaios/plugin-youtube-to-text) - Converts YouTube videos to text transcripts
- [PDF](https://github.com/elizaos-plugins/plugin-pdf) - PDF text extraction and processing capabilities for document analysis

### 💬 Social & Communication

- [Email](https://github.com/elizaos-plugins/plugin-email) - Email client functionality for sending/receiving via SMTP/IMAP
- [Email Automation](https://github.com/elizaos-plugins/plugin-email-automation) - AI-powered email conversation detection and content formatting
- [Echochambers](https://github.com/elizaos-plugins/plugin-echochambers) - Chat room interactions with dynamic conversation handling
- [Twitter](https://github.com/elizaos-plugins/plugin-twitter) - Automated tweet posting with character-aware content generation
- [WhatsApp](https://github.com/elizaos-plugins/plugin-whatsapp) - WhatsApp messaging through the Cloud API with comprehensive features
- [Twilio](https://github.com/boolkeys/plugin-twilio) - SMS, voice, and communication capabilities through Twilio API
- [Lens Network](https://github.com/elizaos-plugins/plugin-lens-network) - Integration with Lens protocol for both native and ERC20 tokens
- [NKN](https://github.com/nknorg/eliza-plugin-nkn) - Communication between multiple AI agents using the NKN protocol
- [Obsidian](https://github.com/elizaos-plugins/plugin-obsidian) - Seamless integration with Obsidian vaults for note management
- [GitBook](https://github.com/elizaos-plugins/plugin-gitbook) - Querying and retrieving information from GitBook documentation
- [News](https://github.com/elizaos-plugins/plugin-news) - Real-time news data through NewsAPI with search and summary
- [Intiface](https://github.com/elizaos-plugins/plugin-intiface) - Controls intimate hardware devices through the Buttplug.io protocol
- [Open Weather](https://github.com/elizaos-plugins/plugin-open-weather) - Weather data using the OpenWeather API
- [Football](https://github.com/elizaos-plugins/plugin-football) - Live football match data and league standings information

### 🔒 Infrastructure & Security

- [0G](https://github.com/elizaos-plugins/plugin-0g) - Decentralized file storage using the Zero Gravity protocol
- [Akash](https://github.com/elizaos-plugins/plugin-akash) - Deployments and cloud compute operations on the Akash Network
- [Anyone](https://github.com/elizaos-plugins/plugin-anyone) - SOCKS proxy configuration for the Anyone protocol proxy services
- [AWS S3](https://github.com/elizaos-plugins/plugin-aws-s3) - AWS S3 integration for cloud-based file storage management
- [DCAP](https://github.com/elizaos-plugins/plugin-dcap) - Intel Data Center Attestation Primitives for secure cloud environments
- [DePIN](https://github.com/elizaos-plugins/plugin-depin) - Connection to physical infrastructure through Decentralized Networks
- [ETHStorage](https://github.com/elizaos-plugins/plugin-ethstorage) - Interaction with EthStorage decentralized storage network
- [Gelato](https://github.com/elizaos-plugins/plugin-gelato) - Smart contract interactions via Gelato Relay on EVM chains
- [Hyperbolic](https://github.com/elizaos-plugins/plugin-hyperbolic) - GPU instance management on the Hyperbolic platform
- [IQ6900](https://github.com/elizaos-plugins/plugin-iq6900) - Blockchain inscription functionality through IQ6900's Code-In standard
- [Irys](https://github.com/elizaos-plugins/plugin-irys) - Decentralized data storage and retrieval using Irys datachain
- [Lit](https://github.com/elizaos-plugins/plugin-lit) - Integration with Lit Protocol for decentralized access control
- [Okto](https://github.com/okto-hq/eliza-plugin) - Self-custody wallet operations and transaction management
- [SGX](https://github.com/elizaos-plugins/plugin-sgx) - Intel SGX attestation capabilities for secure execution
- [Spheron](https://github.com/elizaos-plugins/plugin-spheron) - Deployments and operations using the Spheron Protocol
- [TEE](https://github.com/elizaos-plugins/plugin-tee) - Trusted Execution Environment operations and key management
- [TEE Log](https://github.com/elizaos-plugins/plugin-tee-log) - Secure logging capabilities within Trusted Execution Environments
- [TEE Marlin](https://github.com/elizaos-plugins/plugin-tee-marlin) - TEE verification through Marlin Oyster platform
- [TEE Verifiable Log](https://github.com/elizaos-plugins/plugin-tee-verifiable-log) - Verifiable logging within TEEs for secure, auditable records
- [Thirdweb](https://github.com/elizaos-plugins/plugin-thirdweb) - Access to thirdweb's Nebula AI interface
- [TrustDB](https://github.com/elizaos-plugins/plugin-trustdb) - Trust scores and performance metrics in a secure database
- [TrustGo](https://github.com/TrustaLabs/plugin-trustgo) - EVM account information and MEDIA score attestations from TrustGo
- [Gitcoin Passport](https://github.com/elizaos-plugins/plugin-gitcoin-passport) - Gitcoin Passport API for verifying and managing digital identity
- [GoPlus](https://github.com/elizaos-plugins/plugin-goplus) - On-chain security checks through the GoPlus API integration
- [Quick Intel](https://github.com/elizaos-plugins/plugin-quick-intel) - Token security audits and market analysis across multiple chains

### 🔧 Tools & Utilities

- [Browser](https://github.com/elizaos-plugins/plugin-browser) - Web scraping and browser automation using Playwright
- [DKG](https://github.com/elizaos-plugins/plugin-dkg) - Integration with OriginTrail Decentralized Knowledge Graph
- [Firecrawl](https://github.com/tobySolutions/plugin-firecrawl) - Web scraping and crawling capabilities through the Firecrawl API
- [Gigbot](https://github.com/PaymagicXYZ/plugin-gigbot) - AI-powered freelance work management and automation toolkit
- [Lightning](https://github.com/elizaos-plugins/plugin-lightning) - Lightning Network integration for off-chain Bitcoin payments
- [Stargaze](https://github.com/elizaos-plugins/plugin-stargaze) - Fetches NFT data from Stargaze collections
- [Web Search](https://github.com/elizaos-plugins/plugin-web-search) - Powerful web search capabilities with customizable API interface

---

## 👥 Community

- [elizaOS](https://elizaos.ai) - Official homepage for elizaOS
- [x.com/elizaos](https://x.com/elizaos) - Official X account for elizaOS
- [elizaOS-image-lab](https://rubyfields.github.io/ai16z-image-lab/) - Create an Eliza picture with AI image gen
- [Put on a hat](https://elizaos.github.io/hat/) - Add a hat to any image!
- [awesome-eliza](https://awesome.eliza.fyi) - HTML version of awesome things related to elizaOS/eliza framework
- [elizas world](https://elizas.world/) - List of all agent tokens with contributions

## 🎬 Videos and Spaces

- [The Delphi Podcast: Crypto x AI Agents](https://www.youtube.com/watch?v=HVXxprDVMUM) - Panel with ai16z, Virtuals, MyShell, NOUS, and CENTS
- [What Do Machines Dream Of? Episode 1: World Builders](https://x.com/i/broadcasts/1vOxwrZYbygJB)
- [Threadguy x Shaw Interview](https://www.twitch.tv/videos/**********) - Shaw and Threadguy talking about AI agents during a livestream
- [Hats Protocol - DAO Organization](https://www.youtube.com/watch?v=B5zJxUez2AM) - Overview of Hats protocol and how it applies to elizaOS
- [Bankless + Shaw Interview](https://www.youtube.com/watch?v=5GBXS5myXz0) - What is the role of AI in crypto, and how will it shape the future?
- [Green Pill: Owocki, Shaw, Jin](https://www.youtube.com/watch?v=bnkhu4Bx9C4) - S7 Ep2 talking about Ai Agents, DAOs, and funding public goods
- [jin: Managing Information + Rewarding Contributors](https://www.youtube.com/watch?v=-2PD3uk0Hz4) - Early presentation about information management
- [Shaw + Polygon Labs Interview](https://www.youtube.com/embed/hf7V-IHo5xk) - Discussing decentralized AI, autonomous agents, AGI, and more
- [The Future of AI Agents/w Shaw](https://x.com/thejoven_com/status/1894981534815527187) - Shaw chatting with MotherDAO about AI agents, 2025-02-27
- [DCo - AI Traders, Swarms, and Surviving the Bear](https://www.decentralised.co/p/ep-35-ai-traders-swarms-and-surviving) - Ep35 of The DCo Podcast with Shaw

### 📅 What Did You Get Done This Week?

- [WDYGDTW #1](https://www.youtube.com/watch?v=9EmvhlBPB8Q) - 2024-11-15
- [WDYGDTW #2](https://www.youtube.com/watch?v=Lbvv0Dr91Bc) - 2024-11-22
- [WDYGDTW #3](https://www.youtube.com/watch?v=nUAEQ7uKi04) - 2024-11-29
- [WDYGDTW #4](https://www.youtube.com/watch?v=r3Z4lvu_ic4) - 2024-12-06
- [WDYGDTW #5](https://www.youtube.com/watch?v=4u8rbjmvWC0) - 2024-12-13
- [WDYGDTW #6](https://www.youtube.com/watch?v=R3auUQj9oEg) - 2024-12-20
- [WDYGDTW #7](https://www.youtube.com/watch?v=jcSF7dSicTI) - 2024-12-27
- [WDYGDTW #8](https://www.youtube.com/watch?v=Vs7D5DN_trk) - 2025-01-03
- [WDYGDTW #9](https://www.youtube.com/watch?v=fqM_vYK2bmc) - 2025-01-10
- [WDYGDTW #10](https://www.youtube.com/watch?v=1voboZEQTAw) - 2025-01-17
- [WDYGDTW #11](https://www.youtube.com/watch?v=mxdWvBnxN8M) - 2025-01-24
- [WDYGDTW #12](https://www.youtube.com/watch?v=SZNuoXJ1Mvs) - 2025-01-31

## 📊 Research and News

- [Eliza Technical Report/Whitepaper](https://arxiv.org/pdf/2501.06781) - Academic paper on ElizaOS architecture and capabilities
- [Exploring the Future of AI Agents in Crypto](https://www.binance.com/en/research/analysis/exploring-the-future-of-ai-agents-in-crypto) - Binance
- [Almost all AI agents are just 'memecoins that talk'](https://www.binance.com/en/square/post/18443194059729) - Binance
- [AI16z rebrands into ElizaOS ](https://cryptoslate.com/ai16z-rebrands-into-elizaos-as-adoption-grows/) - CryptoSlate
- [Eliza Labs and Stanford University's FDCI Partnership](https://decrypt.co/296770/eliza-labs-and-stanford-universitys-fdci-to-explore-ai-agents-impact-on-digital-currency-systems)
- [The Bazaar of Agents](https://x.com/0xPrismatic/status/1872624976882512171?lang=en) - Analyzing elizaOS through lens of 'The Cathedral and the Bazaar'
- [The Battle of the AI Agent Frameworks](https://members.delphidigital.io/reports/the-battle-of-the-ai-agent-frameworks#the-frameworks-are-dead-long-live-the-frameworks-75f9) - Delphi research report

---

## 👏 Contributors

Thanks goes to all these wonderful people:

<a href="https://github.com/elizaos/awesome-eliza/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=elizaos/awesome-eliza" />
</a>
