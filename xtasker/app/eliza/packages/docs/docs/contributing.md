---
sidebar_position: 4
title: Contributing Guide
description: How to contribute to ElizaOS - guidelines, standards, and best practices for developers
keywords: [contributing, development, pull requests, issues, documentation, style guide]
---

# Contributing to <PERSON> off, thank you for considering contributing to elizaOS! We welcome improvements and contributions.

## Contribution License Agreement

By contributing to elizaOS, you agree that your contributions will be licensed under the MIT License. This means:

1. You grant us (and everyone else) a perpetual, worldwide, non-exclusive, royalty-free license to use your contributions.
2. Your contributions are and will be available as Free and Open Source Software (FOSS).
3. You have the right to submit the work under this license.
4. You understand that your contributions are public and that a record of the contribution is maintained indefinitely.

## How to Contribute

### For Developers

1. **Extend elizaOS's Capabilities**

   - Develop new actions, evaluators, and providers
   - Improve existing components and modules

2. **Enhance Infrastructure**

   - Review open issues and submit PRs
   - Test and update documentation
   - Optimize performance
   - Improve deployment solutions

3. Fork the repo and create your branch from `develop`.
   1. The name of the branch could start with the issue number and be descriptive of the changes you are making.
   2. Example: 9999-add-test-for-bug-123
4. If you've added code that should be tested, add tests.
5. Ensure the test suite passes.
6. Make sure your code lints.
7. Issue that pull request to the `develop` branch!

## Styleguides

### Git Commit Messages

- Use the present tense ("Add feature" not "Added feature")
- Use the imperative mood ("Move cursor to..." not "Moves cursor to...")
- Limit the first line to 72 characters or less
- Reference issues and pull requests liberally after the first line

### JavaScript Styleguide

- All JavaScript must adhere to [JavaScript Standard Style](https://standardjs.com/).

### TypeScript Styleguide

- All TypeScript must adhere to [TypeScript Standard Style](https://github.com/standard/ts-standard).

### Documentation Styleguide

- Use [Markdown](https://daringfireball.net/projects/markdown/) for documentation.

## Additional Notes

### Pull Request Titles

This section lists the title prefix we use to help us track and manage pull requests. These prefixes must be lower case

- `fix` - Issues that fixes bugs.
- `feat` - New feature, updates or improvements.
- `docs` - Issues or pull requests related to documentation.
- `chore` - General repo maintenance

Please place a colon follow by a space (`: `) in your PR title

### Issue Labels

This section lists the labels we use to help us track and manage issues and pull requests.

- `bug` - Issues that are bugs.
- `enhancement` - Issues that are feature requests.
- `documentation` - Issues or pull requests related to documentation.
- `good first issue` - Good for newcomers.

## Getting Help

- Join [Development Discord](https://discord.gg/elizaOS)
- Join [DAO Discord](https://discord.gg/ai16z)
- Check [FAQ](faq.md)
- Create GitHub issues

## Additional Resources

- [API Documentation](api)
