---
title: ElizaOS Documentation
description: Two distinct paths to master <PERSON><PERSON> - from beginner to expert, we've got you covered
hide_table_of_contents: true
---

import Link from '@docusaurus/Link';

# Welcome to ElizaOS Documentation

<img
  src="/img/eliza_banner.jpg"
  alt="ElizaOS Banner"
  style={{
    maxWidth: '100%',
    borderRadius: '12px',
    boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
    marginBottom: '2rem',
  }}
/>

ElizaOS is a powerful framework for creating and deploying AI agents. Choose your learning path based on your experience and goals:

<div className="row margin-top--lg margin-bottom--lg">
  <div className="col col--6">
    <div style={{ textAlign: 'center' }}>
      <h3 style={{ marginBottom: '1rem' }}>CLI</h3>
      <img
        src="/img/cli.jpg"
        alt="ElizaOS CLI Interface"
        style={{ maxWidth: '100%', borderRadius: '8px', boxShadow: '0 4px 20px rgba(0,0,0,0.15)' }}
      />
    </div>
  </div>
  <div className="col col--6">
    <div style={{ textAlign: 'center' }}>
      <h3 style={{ marginBottom: '1rem' }}>Native GUI</h3>
      <img
        src="/img/gui.jpg"
        alt="ElizaOS Native GUI"
        style={{ maxWidth: '100%', borderRadius: '8px', boxShadow: '0 4px 20px rgba(0,0,0,0.15)' }}
      />
    </div>
  </div>
</div>

<div className="row margin-top--lg">
  <div className="col col--6">
    <div
      className="card shadow--md"
      style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        borderRadius: '12px',
      }}
    >
      <div className="card__header">
        <h2 style={{ color: 'white' }}>🚀 Quick Start</h2>
        <p className="subtitle" style={{ color: 'rgba(255,255,255,0.9)' }}>
          Get up and running in 5 minutes
        </p>
      </div>
      <div className="card__body">
        <p style={{ color: 'rgba(255,255,255,0.95)' }}>
          Perfect for creators, business users, and anyone who wants results immediately.
        </p>
        <ul style={{ color: 'rgba(255,255,255,0.9)' }}>
          <li>✨ One-click deployment</li>
          <li>🎯 Ready-to-use templates</li>
          <li>📱 Multi-platform setup</li>
          <li>⚡ No coding required</li>
        </ul>
      </div>
      <div className="card__footer">
        <Link
          className="button button--secondary button--lg button--block"
          style={{
            background: 'rgba(255,255,255,0.2)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.3)',
            color: 'white',
          }}
          to="/docs/simple/getting-started/quick-start"
        >
          Start Here →
        </Link>
      </div>
    </div>
  </div>

  <div className="col col--6">
    <div
      className="card shadow--md"
      style={{
        background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
        color: 'white',
        borderRadius: '12px',
      }}
    >
      <div className="card__header">
        <h2 style={{ color: 'white' }}>⚡ Developer</h2>
        <p className="subtitle" style={{ color: 'rgba(255,255,255,0.9)' }}>
          Build the next generation
        </p>
      </div>
      <div className="card__body">
        <p style={{ color: 'rgba(255,255,255,0.95)' }}>
          For developers building custom plugins, integrations, and contributing to ElizaOS.
        </p>
        <ul style={{ color: 'rgba(255,255,255,0.9)' }}>
          <li>🏗️ System architecture</li>
          <li>💻 Plugin development</li>
          <li>🔍 Complete API reference</li>
          <li>🚀 Advanced patterns</li>
        </ul>
      </div>
      <div className="card__footer">
        <Link
          className="button button--secondary button--lg button--block"
          style={{
            background: 'rgba(255,255,255,0.2)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.3)',
            color: 'white',
          }}
          to="/docs/technical/architecture/overview"
        >
          Dive Into Code →
        </Link>
      </div>
    </div>
  </div>
</div>

## 🧭 Which Path Is Right for You?

<div className="row margin-top--lg">
  <div className="col col--12">
    <div
      style={{
        background:
          'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.1)',
        borderRadius: '12px',
        padding: '24px',
      }}
    >
      <div className="row">
        <div className="col col--6">
          <h4>🚀 Choose Quick Start if you:</h4>
          <ul>
            <li>Want an AI agent running today</li>
            <li>Prefer guided, visual instructions</li>
            <li>Focus on content over code</li>
            <li>Need proven, working examples</li>
          </ul>
        </div>
        <div className="col col--6">
          <h4>⚡ Choose Developer if you:</h4>
          <ul>
            <li>Have programming experience</li>
            <li>Want to build custom plugins</li>
            <li>Need system integrations</li>
            <li>Want to contribute code</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

## 🌟 Popular Starting Points

<div className="row margin-top--lg">
  <div className="col col--4">
    <Link
      className="card padding--lg hover-lift"
      to="/docs/simple/getting-started/quick-start"
      style={{ height: '100%', textDecoration: 'none' }}
    >
      <div style={{ fontSize: '2.5rem', textAlign: 'center', marginBottom: '12px' }}>⏱️</div>
      <h4>5-Minute Setup</h4>
      <p style={{ color: '#666', fontSize: '0.9rem' }}>
        Get your first agent running with zero configuration
      </p>
    </Link>
  </div>
  <div className="col col--4">
    <Link
      className="card padding--lg hover-lift"
      to="/docs/simple/templates/gallery"
      style={{ height: '100%', textDecoration: 'none' }}
    >
      <div style={{ fontSize: '2.5rem', textAlign: 'center', marginBottom: '12px' }}>🎭</div>
      <h4>Character Gallery</h4>
      <p style={{ color: '#666', fontSize: '0.9rem' }}>
        Ready-to-use personalities that work out of the box
      </p>
    </Link>
  </div>
  <div className="col col--4">
    <Link
      className="card padding--lg hover-lift"
      to="/docs/technical/architecture/core-concepts"
      style={{ height: '100%', textDecoration: 'none' }}
    >
      <div style={{ fontSize: '2.5rem', textAlign: 'center', marginBottom: '12px' }}>🧠</div>
      <h4>Core Concepts</h4>
      <p style={{ color: '#666', fontSize: '0.9rem' }}>
        Understand ElizaOS architecture and patterns
      </p>
    </Link>
  </div>
</div>

## 🎯 Learning Progression

<div className="margin-top--lg">
  <div
    style={{
      background: 'linear-gradient(90deg, #667eea 0%, #4facfe 100%)',
      height: '4px',
      borderRadius: '2px',
      marginBottom: '20px',
    }}
  ></div>
  <div className="row">
    <div className="col col--6 text--center">
      <h4>🚀 Start Simple</h4>
      <p>Get your first agent running and understand the basics</p>
    </div>
    <div className="col col--6 text--center">
      <h4>⚡ Then Build</h4>
      <p>Create plugins, contribute code, and push boundaries</p>
    </div>
  </div>
</div>

## What is ElizaOS?

ElizaOS is a powerful multi-agent simulation framework designed to create, deploy, and manage autonomous AI agents. Built with TypeScript, it provides a flexible and extensible platform for developing intelligent agents that can interact across multiple platforms while maintaining consistent personalities and knowledge.

<div style={{ textAlign: 'center', margin: '2rem 0' }}>
  <img
    src="/img/eliza-architecture.jpg"
    alt="ElizaOS Architecture"
    style={{ maxWidth: '100%', borderRadius: '12px', boxShadow: '0 8px 32px rgba(0,0,0,0.12)' }}
  />
  <p style={{ fontSize: '0.9rem', color: '#666', marginTop: '0.5rem', fontStyle: 'italic' }}>
    ElizaOS Multi-Agent Architecture
  </p>
</div>

### Key Features

- **Platform Integration**: Clients for Discord, Telegram, Farcaster, and many others
- **Flexible Model Support**: Deepseek, Ollama, Groq, OpenAI, Anthropic, Gemini, Llama, xAI (Grok), etc.
- **Character System**: Create diverse agents using [character files](https://github.com/elizaOS/characterfile)
- **Multi-Agent Architecture**: Manage multiple unique AI personalities simultaneously
- **Memory Management**: Easily ingest and interact with documents using RAG
- **Media Processing**: PDF, URLs, Audio transcription, Video processing, Image analysis, Conversation summarization
- **Technical Foundation**:
  - 100% TypeScript implementation
  - Modular architecture
  - Highly extensible action and plugin system
  - Custom client support
  - Comprehensive API

<div style={{ textAlign: 'center', margin: '2rem 0' }}>
  <img
    src="/img/montage-plugins.jpg"
    alt="ElizaOS Plugin Ecosystem"
    style={{ maxWidth: '100%', borderRadius: '12px', boxShadow: '0 8px 32px rgba(0,0,0,0.12)' }}
  />
  <p style={{ fontSize: '0.9rem', color: '#666', marginTop: '0.5rem', fontStyle: 'italic' }}>
    Extensive Plugin Ecosystem
  </p>
</div>

## 🌍 Community & Support

<div className="row margin-top--lg">
  <div className="col col--3">
    <Link
      className="card padding--md text--center"
      to="https://discord.gg/elizaos"
      style={{ textDecoration: 'none', height: '100%' }}
    >
      <div style={{ fontSize: '2rem', marginBottom: '8px' }}>💬</div>
      <h5>Discord Community</h5>
      <p style={{ fontSize: '0.85rem', color: '#666' }}>
        Get instant help and share your creations
      </p>
    </Link>
  </div>
  <div className="col col--3">
    <Link
      className="card padding--md text--center"
      to="https://github.com/elizaOS/eliza"
      style={{ textDecoration: 'none', height: '100%' }}
    >
      <div style={{ fontSize: '2rem', marginBottom: '8px' }}>💻</div>
      <h5>GitHub Repository</h5>
      <p style={{ fontSize: '0.85rem', color: '#666' }}>Source code, issues, and contributions</p>
    </Link>
  </div>
  <div className="col col--3">
    <Link
      className="card padding--md text--center"
      to="/docs/simple/templates/gallery"
      style={{ textDecoration: 'none', height: '100%' }}
    >
      <div style={{ fontSize: '2rem', marginBottom: '8px' }}>🎨</div>
      <h5>Template Gallery</h5>
      <p style={{ fontSize: '0.85rem', color: '#666' }}>Discover and share character templates</p>
    </Link>
  </div>
  <div className="col col--3">
    <Link
      className="card padding--md text--center"
      to="https://youtube.com/@elizaos"
      style={{ textDecoration: 'none', height: '100%' }}
    >
      <div style={{ fontSize: '2rem', marginBottom: '8px' }}>📺</div>
      <h5>Video Tutorials</h5>
      <p style={{ fontSize: '0.85rem', color: '#666' }}>Learn with step-by-step video guides</p>
    </Link>
  </div>
</div>

<style>
  {`
.hover-lift:hover {
  transform: translateY(-4px);
  transition: transform 0.2s ease;
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card {
  transition: all 0.2s ease;
}
`}
</style>
