---
id: complete-message
title: 'Complete message processing'
description: 'Notify that message processing is complete'
sidebar_label: 'Complete message processing'
hide_title: true
hide_table_of_contents: true
api: eJztV01v2zgQ/SvEXLYIVDtfd9c3725hwMAGLdoUe2hyGJMjmxuKVEjKjtfwfy+GlKzUSZMiSE/bhSV6Zt48vnkhp0hFykiVCwU6N4HDOckZugqpA8oZng8XWjvLGUpJaFawxGmI1MIKA5zx71Ah0JLV1DGYkJJENjGuQnPisVG1Cxn6sjQnId+DRXcIOep5+jpIHkrkzlITUs/8M6mEKdkmwBhomWKZGg1Ic7Ql+F1l4PpYoBkOpNXCRGvKsdI1qed5T8obWXVEMrLWiAJTLTm0bzj9lFDGjUF8ktNvb72b62QTZNGA/IEkSYAEFzKjM5oe+AqrY54GdwPVJmE0b14hwMIwTQ7iMBbFfEaMNRwdrO6iC2Ecjj0cx7YxC86wJaYy5lsK4fmQOhJLWu9fQAmIr16dYdP4RtwHcGxSbiz9vQ0pMRo9UIUpSfAEaWpCgdRKJ9WtFS1IfD07OT+dTFJ/fjOZz8J7yhcBHs1n9Hoc7n2I8TDeHkK6xmu8n/q3+CmZ7Sbk5/TVw7Q9DTTJRE3MZFWeP/mFdXbTk2prxP7L5xkDh4wywjqYqo2wEGFi5d8h6CUfO8ITmSzhp9F4NA5uodgItWU/m8m1cfx/HP79ieur/iFcOWcYfZCj23vMhZRSKCE2LU7xjZlzaKGRFL+WypJ6phyWihagJ+dAvm+hdgB1H04OdVn3mEypwyQtTaGdT92tiAC1Uk4MKyfP+YJJCNy8lEMXKgJAUCiJJz6Ov4zhZnJ8cTo5uTgdBZ8vfhJNiSGfJCmfxQrFBrNT43qXeBpcxnCJL2RGRyycHriCdJWn8d04FzGjWfMKpAhLwzQpQnGICl8U85mFBtDYcHW0uoMjFDk8fQ+HFD8YXL05waaBjLjfwXFJubH097Yb8m9Y1kQVpiRhEWSoCQVSK51UrQoUJI6evRxlT+DfOQRMIsE4SSXGMSksaJJKvEPItlia3awITROJZhSJWWSR6BVmhfF5U6mSBTlFPT19KpNLZE14XerDRYlTRwbXKcUi8awrcIKVeKqT/8X8SRfUGUWbeGIFxdzT2sssyZyeYdRRyX4gU6yiWLqmI0JLlGVDFiGC+qdvvjUWHmLCp0dvZHO2lbxNK0pKMLlqI7eCy9Aw3IZLLd+G1iQfB9uxiqHKbof8bDIZvWEfJn9mgurW0Mo6h6yeKvGKQmzMiFMbnC0A8IqC3xmw8IYpnk7PB5OT0+fT88nF8Px0OIlGYpHGIjEgH5nIqNyKKVDGGb90sEIaOGfvBxGY5ynz3nA7Kw1aWoiJvo5Hs+/xG/w50n2kGdGgZ1Dl5Kps5Mck6oyx+MaULCVrJo4Bx7m0ydnRKa/FKBCxEWoNTzjdYaPYdkmKOdBGGJrEq5SmpNAHJo0UyoUKJUMhIdO1dI2x1DjsxDrdpKQEt5Xg2srKKlGKWru1TI5GYoEjDaqwOmx20sHa6rZMPJJMXnE3qXeGy+l09sgUPLedJqkuElMJa6IK9yjbGcI9RN+mKqKOpRpRu44qF61jQnMHRaeyBijQO1O4V4wi4BNE+URNQghNKWajgmKqREc9cmxE3R7/xE7E0hJcO7Vtoq2NpNqKSJYpCM3dJLS2crIC17hJgzwVGkctv0gBOpDZRPCIhudCNKB/F7qMvp5xCWP/xPH4JVd0u3z/D/z407T7/SMA2+7BEMRZRkPvCtaqDkz2pI86Ml/+4Jq2Bk9nlSsqJW3fIKNcgJj1I+L28jYqCoDJ4rOm0n5++AsU2eLGP7cCUcFoYgYa+93f/0bvFoY8Xzfsv3rj3lQTdGt1Q6PHqDp1XdnTmB5D6U8AdQTSLB732nk2BXd2jbKWxOUj2zU6QlI9s/0gB0dJAQiiSNeEFGTY+ceCVUKcREKZp2+SNnhRaW4/hLkgiMT4By4pIaiAkwSKq4JIjL/jUkZMzn7Bi8fl/PoY2y6qTqmFuL/5d2GCDwFXq9DDroLsP8d7+6OkjNPB3oPVFKcCkQaykN0d/194v8X2
sidebar_class_name: 'post api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading
  as={'h1'}
  className={'openapi__heading'}
  children={'Complete message processing'}
></Heading>

<MethodEndpoint
  method={'post'}
  path={'/api/messaging/complete'}
  context={'endpoint'}
></MethodEndpoint>

Notify that message processing is complete

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails parameters={undefined}></ParamsDetails>

<RequestSchema
  title={'Body'}
  body={{
    required: true,
    content: {
      'application/json': {
        schema: {
          type: 'object',
          required: ['channel_id', 'server_id'],
          properties: {
            channel_id: { type: 'string', format: 'uuid', description: 'ID of the channel' },
            server_id: { type: 'string', format: 'uuid', description: 'ID of the server' },
          },
        },
      },
    },
  }}
></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '200': {
      description: 'Completion event emitted',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              message: { type: 'string' },
            },
          },
        },
      },
    },
    '400': {
      description: 'Missing or invalid fields',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: { type: 'string', description: 'Error message' },
            },
            title: 'Error',
          },
        },
      },
    },
    '500': {
      description: 'Failed to notify message completion',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: { type: 'string', description: 'Error message' },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
