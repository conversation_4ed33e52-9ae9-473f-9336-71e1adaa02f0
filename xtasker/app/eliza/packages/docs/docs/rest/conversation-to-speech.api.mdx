---
id: conversation-to-speech
title: 'Process conversation and return speech'
description: 'Processes a conversational message and returns synthesized speech'
sidebar_label: 'Process conversation and return speech'
hide_title: true
hide_table_of_contents: true
api: eJztV0tz2zYQ/iuYPbUe2lTa9MKbm6gzmmkaj61MD7EPK2BFIgEBBgBlKxr998wClEg/WvuQySm6iCT28WF3sd9iBxHrANVHwF5pBwVgTTYGuClAUZBed1E7CxVceCcpBAoChXR2Qz4gL6ERLYWANQm0SniKvbdBhK2NDQX9lZQIHZFsoADXkU9KCwUVTK0s3dVBqEOPLUXyDGsHFluCKsNaKChAM5oOI8t6+tJrTwqq6HsqIMiGWoRqB3HbsVqIXtsaClg732KECvpeK9g/3N3irXBrERsSyRHsb7JxCvFPp7Zs8aEv6Wxk0WoH2HVGy7SR8lNgg7vHUNzqE8nIG/Qch6gppFW6i08Bvo9vSXfxEGdG751rOYjP6V0614rFW1YhG3XcvkRpniQHtT6Q/yfl4Dm1D4G8SOnaF0PaXugpK6Ufhz10zoYcnd9mM/67r3XOpSpC9IStuNWxebrapglijbLtqH4yNY+rZKUt+i1kRK+fArGwGzRa5XoRXD9etDoEbWuRcvrdKiT0kk/eRHDlnCG0UADdYdsZgmqNJhBn2Xvnn7cpnXpJetiYSLL7Ag7l90K1SbUqiqhNeF7zbRIkJdI2hLY5IbyYUxF15N1mF8f0vH6iRlJarIti7XqrfmbjR2Xjj6cOS8bQZQrhEzLt/T9z82Nyk4DFxjH3di6krTKNVlBip8tM/OVuYNp9mRtp+SBVgfzmwM29N1BBE2NXlaVxEk3jQqx+n81mjwD/zctC0YaM61o+nNlSolpO9OVIt/NDrg70OEbhQHzjl5HXxm8jaY3f7L33PQ8Sa5dcHCJl9FcU76/E+cXiEfzzi4VQTvaMPIVCrJ0XR5XNq7PZ2auZOBXnYm3oTq9MnoeCRIP8cr4YqGLN482t85/Pru21XTY6sEOhg1AUdG1JiejEikQfSGV6Q5GCa7bC99by+cmOtQ0RraQzMbeqc9rGINAYd5vASU8Yta2La9uixZofEyZtI3mUvJbtH7AFERvv+po9Xs6vlllyjZIGrHSA6nyNNtGtttGlwWnt2DPbVK5FbUN1bU/FycnVNkRqT04qkZ9Ob7UicRwFQ4JEdqO9s6kuEljix2wgtfLABnJTN3pNciuHAI/SE5tZ8R21zm9HxTa95zGV56KHjt6lc6htzSrvhqG2QatMCp1s0FoyIQdxOAcDRJ4vkqM0mkwaXRJNB+k4pBzRKY2s85c2JPrOOFR5R7zwCNxyPmfhpe9DJCXmdyT7VIfzSegeRuBfWl05+Zkiq14SmtOoWxLStW1vhy4rNhpFljpbvIcCeFu56HNRD/0ZZerPwzE6Vv6bbCtu+dgd+0GoyrLWselXZ9K1JbG0C/kfuBNxA2rRTiwOF4x71DC5Uoxz3b2DuRu547tcUYauy22n7Axqm2Zg3tdu6JYfATs9XpQKqMa7yXT2HJvmTQHcF1l1t1thoA/e7Pf8+UtPfgvVx5sCNug1N4rUWJUO/KwGtvqfPf9yOVxLfhUP7zD/sZnhI1rO2AZNz29QwGfaTu5Z+5t9AQ2hIp8Q5dU32e/pkm2M2o84e18cNM6lpC5OZcdRnDv/kZEu3l8toYDVcNtqE+2Cx1tu+nibMboUgUSP6dsODNq6T1wL2TP/vgG79yqs
sidebar_class_name: 'post api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading
  as={'h1'}
  className={'openapi__heading'}
  children={'Process conversation and return speech'}
></Heading>

<MethodEndpoint
  method={'post'}
  path={'/api/agents/{agentId}/speech/conversation'}
  context={'endpoint'}
></MethodEndpoint>

Processes a conversational message and returns synthesized speech

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails
  parameters={[
    {
      name: 'agentId',
      in: 'path',
      required: true,
      schema: { type: 'string', format: 'uuid' },
      description: 'ID of the agent',
    },
  ]}
></ParamsDetails>

<RequestSchema
  title={'Body'}
  body={{
    required: true,
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            text: { type: 'string', description: 'Text message' },
            roomId: { type: 'string', description: 'Room ID' },
            entityId: { type: 'string', description: 'Entity ID' },
            userName: { type: 'string', description: 'User name' },
            name: { type: 'string', description: 'Entity name' },
          },
        },
      },
    },
  }}
></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '200': {
      description: 'Audio stream with synthesized speech',
      content: { 'audio/mpeg': { schema: { type: 'string', format: 'binary' } } },
    },
    '400': {
      description: 'Invalid agent ID or missing text',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '404': {
      description: 'Agent not found',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '500': {
      description: 'Error processing conversation',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
