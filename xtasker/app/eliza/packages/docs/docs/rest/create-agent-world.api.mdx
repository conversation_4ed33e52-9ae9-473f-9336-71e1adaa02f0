---
id: create-agent-world
title: 'Create a world for an agent'
description: 'Create a new world for a specific agent'
sidebar_label: 'Create a world for an agent'
hide_title: true
hide_table_of_contents: true
api: eJztWEtv2zgQ/isDntpAsd3d7kW3bOoFDGy3QZKihyaHMTmW2FKkSlJ2XMP/fTGk/G6bFlsEe2gulsSZj8OZbx7MSkSsgijfC6zIxiDuC6EoSK/bqJ0Vpbj0hJEAwdICFs4bBTPnASG0JPVMS0iaohCuJY+sNVGiFDLpXfDaO9YShWjRY0ORPG+4EhYbEmXeeMLrmvdrMdaiEJ4+ddqTEmX0HRUiyJoaFOVKxGXLaiF6bStRiJnzDUZRiq7TSqyP7Z+8AjeDWFNv5/o+g1OIfzq1ZMTjvaSzkUXLlcC2NVqmUw0/BAZcnZriph9Isgdazz6ImgKv5vOdGnxo3z/Y0MbC5F4+QnCdl3SbFB8DYCkGyDrwjAbVoAClg3ReFRDJUOWxeb7D5fg8hnplMLJnz7dh1ops1DNNnpEaiqgwftENh0gXSml+RNPTZ6u6Tn8cjtA6G7LXfhu94J9DjMQgyJRSEDopKYRZZ8xS/LRw9ah7glPnDKEVhaAHbFpDmSBMsa+c/BAxh/NRMf3FcBzS+tipb63+1NFeTFJSHnDoP9Bvk5I/bNZRskGsMYJb2ACx1uHn8PuE2QVQlP9Lehci6si0yQTuCc/fX45Gpyyf2DkarXrfsS899KXqiWk+QxOY5+S9849jSqe+I5BjBoMkmzwcAlbfrbYRT/U9ojbhcc1XSZAUpGOAtpm8vJijsIlO2kJsIvPyNDKpjYF1EWaus+pXNJ4qGn98KU9607kbaFv1VeVXSJ4kJLk21o5HvNalwpQmtlIMsdXDPEUOV30HWQ9TcIIoRCA/30x+nTeiFHWMbTkcGifR1C7E8vfRaHRi49+8DIrmZFzbcBpmpDTIcWyvd8PceBOe7XC5Pfh+xzn+mubV7be90s+nZackT25cYfRnhDc3cHE1OW0GVxNQTnZsZ/JjastblfmLwWjwYgTncAEzQw96agjQKggSDfLLxaSv/jMelRfOfxzc2Tt7y92TwXUARUFXlhREB1OCLpCChY41ICRXmiX4zlrOjLyxtiGilTSAsVWt0zYGQGPcIhm3SaPizjZoseLHZJO2kTzKnGKMv7GNm7l3XcU7Xo9vbrPkDCX1ttLGVOcrtPozJTCX5oKZ450ZU7kGtQ3lnT2Hs7ObZYjUnJ2VkJ/OF1oRbK8UIZlEdq69s4kFyVjixwyQSnRggFysjZ6RXMrewTvpPcys+Joa55c7xSa9Jy3vXHOy0euUaNpWrJJfCGq0yiTXyRqtJROyE3vW9yZ2Sru0ET9A6x0XGvZFEm2JZA1haWNNQW+tUxpZ5y9tCLrWOFT5RLxwYtzteMzCt74LPCmPH0h2iYfjPdcde+AdTW+c/EiRVa8JzXnUDYF0TdPZvozCXCNkqcHkjSgEHyuTPpO6L8Ao4176bZl/mbEiz+u77A/lcFjpWHfTgXTNkFjahfybxieuMA3aPcTtdXTvKmq3V9CDbFztOsIP3GL7ihnpIQ5bg9pyRU0mr/pK915gq0WxuTEXotxdYftyd18Irmgsu1pNMdBbb9Zr/vypI78U5fv7QszRa076VBKVDvys+tbyjaM8u+6vq8/h+G77Fev7j2jZ+3M0Hb+JQnyk5d79e32/LkRNqMgni/LqZd73PFXOnfZJg10XG40LKamN35S932siV29ubkUhpv1dvEmdUnhc8D8BcJEtdckPqaOlbyth0FZdao8iY/Lfv0j01o0=
sidebar_class_name: 'post api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading
  as={'h1'}
  className={'openapi__heading'}
  children={'Create a world for an agent'}
></Heading>

<MethodEndpoint
  method={'post'}
  path={'/api/agents/{agentId}/worlds'}
  context={'endpoint'}
></MethodEndpoint>

Create a new world for a specific agent

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails
  parameters={[
    {
      name: 'agentId',
      in: 'path',
      required: true,
      schema: { type: 'string', format: 'uuid' },
      description: 'ID of the agent',
    },
  ]}
></ParamsDetails>

<RequestSchema
  title={'Body'}
  body={{
    required: true,
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            name: { type: 'string', description: 'Name of the world' },
            sourceType: { type: 'string', description: 'Type of source (e.g., discord, telegram)' },
            sourceId: { type: 'string', description: 'Platform-specific identifier' },
            metadata: { type: 'object', description: 'Additional world metadata' },
          },
        },
      },
    },
  }}
></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '201': {
      description: 'World created successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              data: {
                type: 'object',
                properties: {
                  world: {
                    type: 'object',
                    properties: {
                      id: {
                        type: 'string',
                        format: 'uuid',
                        description: 'Unique identifier for the world',
                      },
                      name: { type: 'string', description: 'Name of the world' },
                      agentId: {
                        type: 'string',
                        format: 'uuid',
                        description: 'ID of the agent that owns this world',
                      },
                      sourceType: {
                        type: 'string',
                        description: 'Type of source (discord, telegram, etc)',
                      },
                      sourceId: { type: 'string', description: 'Platform-specific identifier' },
                      metadata: { type: 'object', description: 'Additional world metadata' },
                    },
                    title: 'World',
                  },
                },
              },
            },
          },
        },
      },
    },
    '400': {
      description: 'Invalid agent ID or request',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '404': {
      description: 'Agent not found',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '500': {
      description: 'Error creating world',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
