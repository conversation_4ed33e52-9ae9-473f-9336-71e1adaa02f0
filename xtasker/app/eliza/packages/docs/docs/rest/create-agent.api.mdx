---
id: create-agent
title: 'Create a new agent'
description: 'Creates a new agent from character configuration'
sidebar_label: 'Create a new agent'
hide_title: true
hide_table_of_contents: true
api: eJy9V0tv4zYQ/isELwUCJXbannRLty6QRXcTbLzoIclhRI6l2VCkQlJxtIb/ezGkEjl20M12i/piPWa+eX9DbWSEOsjyWkKNNgZ5W0iNQXnqIjkrS/nOI0QMAoTFtUhSYuVdK1QDHlREL5SzK6p7D0mlkK7DfH2uZSlVAjhjRVlIj/c9hvib04MsN+mWPGpZRt9jIZWzkQXLjYSuM6QSzuxLYF82MqgGW+CrOHQoS+mqL6gYt/NsNRIGfvvs2yXEZkc8RE+2lvsxspSITsBOUCsyKLfFBPV+9GHf8l66Xs+KICveX118FCvnW4hym36cjtA5G7LXP89P+e8lYEqcyEnUIvRKYQir3phB/mfpGlF3BCvnDAIXEx+h7QzmAm0LqSF+TwFeE52Kfi0ttMhN91Kd9GtFG3NXyr4nfZD5z5buexSk0UZaEZfQeREbnIrK9UwGv9kRH6FF4VaH6hW5b2tfNc5HUZGrPXTN8CpQwBjJ1uE7Wuo4dKhoRUo8KzPQECK2b3AqyYnOu7aLr+cmxMHgdzj0UxDKtW1vx8YTGWBbSOP8LhB4D9yvFLENh65u9y0sHiNajVowjgCrRQXqrvaut1qQzY3AottCthgC1LjIfRp+yGqCECNiSEmaGCF6IDsqRteR+hFbywTwsgSCgrizbm1Q1wiVQQGV6yMrd6avyf6IwcuMIPqAWlTDfvE5Jopc/am8I0vxu1/n80NqWnjPCWJqIlvn3fA/k9IKTGBWQnblDbTk9BuGf4yLZaf+eqvak3gqQAQyrxRnX/P3JIhapDBeNvh2tzTJhBxr0mJsHG/YzoUUatp1cgYdzcZtzjTjH9Dzht/I3htZyibGrpzNjFNgGhdi+ct8Pj9w6U9+LTQ+oHFdyysoI8ntbSG5lJ+mRb54qsbB4p0C3l+jHADHmZLzFJ2hryAursTZ5fmBQ2eX50I71bMvmWx4Op9VHk5P5ienc3EszsTK4COl8bFaBAUmzdLZ+fPpBVpcO393cmNv7LKhwAZ5+DQGqi1qPgtUmEdlTbERIFK6zCB8b5kDRsNkQwSr8EQsrO4c2RgEGOPWmTrGyShubAsWar5MPpGNyNlgoIT/5BsTgnd9zRY/La6WWXIFCkdf8clV52uw9BUTmEvTvHJsmTG1a4FsKG/ssTg6ysR/dFSKfHW8Jo3i+ZAWkktoH8g7myqdnEW+zADpCBIYIB9GDK1QDWpM8CS9g5kVP2Dr/DAptuk+aXnn2gNDH9LskK1ZJd+gaMBqk1KnGrAWTchJHDt7dLHX5JIhvuAlx9yRWIlFO0TViDDY2GCgZ+80Aev8QQZF3xkHOkfELw6cWy4WLLz0feCT2OIRVZ/6cLGTuv0M/IXVlVN3GFn1E4I5jtTi3tZ8IBBZ6uT8QhaSw8pNn5t65FRQiVPzAWYalncZK/IymCY8lLNZTbHpqxPl2hmytAv5P7E9k0YLdgcxn/R3D/r7Q7iZuP3ffBeMJBjxMc46A5TWd3J5M5LXtYSOZLHzMcIMxc83mwoCfvZmu+XH9z36QZbXt4V8AE884Hy3LWSDoPnYeb2Rdziwo9nl4yVbZ3HTp/25v5q2xZPGmVLYxX+Uvd2h38uLq6UsZDV+0rRpx0gPaz7qwlqWMn0Tpa5I3zz8bCMN2LpPi0VmTP79DQ7Wv0s=
sidebar_class_name: 'post api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'Create a new agent'}></Heading>

<MethodEndpoint method={'post'} path={'/api/agents'} context={'endpoint'}></MethodEndpoint>

Creates a new agent from character configuration

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails parameters={undefined}></ParamsDetails>

<RequestSchema
  title={'Body'}
  body={{
    required: true,
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            characterPath: { type: 'string', description: 'Path to a character file' },
            characterJson: {
              type: 'object',
              description: 'Character configuration in JSON format',
            },
          },
        },
      },
    },
  }}
></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '201': {
      description: 'Agent created successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              data: {
                type: 'object',
                properties: {
                  character: {
                    type: 'object',
                    required: ['name'],
                    properties: {
                      id: {
                        type: 'string',
                        format: 'uuid',
                        description: 'Unique identifier for the character',
                      },
                      name: { type: 'string', description: 'Name of the character' },
                      bio: { type: 'string', description: 'Short biography of the character' },
                      settings: { type: 'object', description: 'Character-specific settings' },
                      system: { type: 'string', description: 'System prompt for the character' },
                      style: { type: 'object', description: "Character's communication style" },
                      lore: {
                        type: 'array',
                        items: { type: 'string' },
                        description: 'Extended lore and background information',
                      },
                      messageExamples: {
                        type: 'array',
                        items: { type: 'string' },
                        description: 'Example messages for character training',
                      },
                      topics: {
                        type: 'array',
                        items: { type: 'string' },
                        description: 'Topics the character is knowledgeable about',
                      },
                      plugins: {
                        type: 'array',
                        items: { type: 'string' },
                        description: 'Plugins used by the character',
                      },
                    },
                    title: 'Character',
                  },
                },
              },
            },
          },
        },
      },
    },
    '400': {
      description: 'Error creating agent',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
