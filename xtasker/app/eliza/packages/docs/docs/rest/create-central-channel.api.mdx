---
id: create-central-channel
title: 'Create central channel'
description: 'Create a channel in the central database'
sidebar_label: 'Create central channel'
hide_title: true
hide_table_of_contents: true
api: eJztV0tv2zgQ/ivEHAM5dpom3dUtm3oBA9tN0KTYQ5LDmBxZbClSJSknrqH/vhhKthW7jywK9LS+mKRmPn6cJ7mGiIsA+R1UFAIutF3AQwaKgvS6jtpZyOHSE0YSKGSJ1pIR2opYkpBko0cjFEacYyDIwNXkkdVmCnKQSfGyE7vslCEDT58bCvEPp1aQr9NUe1KQR99QBtLZSDbyJ6xro2UCHH8MTGYNQZZUIY/iqibIwc0/kow9bgd0BxYr5hPIL8nPFB+q9swuagqsnAR2ICF6Pns7UDn4mEHhfIURcmgarVj4maG+AtYtHAKRbSqmGemJmS+dlkxXVZDBwrumhoc2/fhUoXY2dKxfTU74b88/vR827unMrkRopKQQisaYFfyEXZ8brkcdCM6dM4Q22QPjCyB6oj8W1C/wwn60frD6c0NCK7JRF5q8KJzv4rXfts2+4f19qL+xIuGKfeWXR8g+4OztBq7DELHUYeu3ORlnF0FE95Ohs7/t7apO5xic4buBe1AAeoLD1TaDiiJ+y+HPAS6U0jwcBOlWmWM86mhotxG07Sb6X08mhwE/s0s0Wom+kPyK2M6AnrCqmWWBJlCbAXnv/AuC3akXhNqUwUSSTZblYvxitY148mtEbcKPNd8mQVIiHUNo20Vucm079Enaol87+5o3eupcdLRdbIPsf6f8Eqd0eVg67re1S8lQYywhhzHWerxt6+O+W496/4Rtd+T2v4bGG8ihjLHOx2PjJJrShZifTiaTA5p/8WehaEnG1RXZ2JczaB8yYPe+33X46cZDm5a7O/uujsJpgb+dFeevR2dvTt6MXp+dvxrNTws5eiV/Pz8tzs+xwPMDGlug3qipIrYZsNmSrTfGMvoLiqsbcXE9OyxN1zOhnGz4GMnSqV1sVZYnx5Pjk4kYiQtRGHrSc0MCrRJBokGeXMwELtgGhceKHp3/dHxv7+0tV3YG14Hrpl5YUiI6MSfRBFLiUcdSoEiWNivhG2s5e7qNtQ0RraRjMbWqdtrGINAY95jIbVItu7cV2uTdLHHSNpJHmdIw4W+4BRFL75oF7/h+enPbSRYoqedKG6rOL9DqL5TAXGpVheOdGVO5CrUN+b0diaOjm1WIVB0d5aIbjR61IrG9AYZEiexSe2dTkCSyxMMO4CJRY4A0EkYXJFeyN/BOeoDZKb6jyvnVTrFK86TlnasONnq3SQJW6SYkSrTKJNNtMqIzYp8UPcVGaZc24oGoveNSxLZIojWRLEVY2VhS0Ft2SiPr/KkNiaY2DlV3Iv5wQO52OmXhW98EvrRNn0g2KQ6nA9PtW+Afmt84+Ykiq74nNKOoKxLSVVVj+0IrlhpFJ3U8u+KrAvnQBX0X1H2JRhkH2bmN/MsOK/LVcVccQj4eL3Qsm/mxdNWYWNqF7j+**************************//y4Bik/rg2qNPFJNFd93XwDrDWkA0eOBkc1MKHDLjcsfR6zdAfvGlbXv7ckF9BfveQwRK95pTnWZtBSajIp+L5iVZMuzvAiK9bbG40DXM76H1tttG4kJLq+F3Zh0F9v766uYUM5v3DqUpNDDw+8uMHHyGH9ARLcZJeVry2BoN20aTOBR0m//4FNi3YVQ==
sidebar_class_name: 'post api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'Create central channel'}></Heading>

<MethodEndpoint
  method={'post'}
  path={'/api/messaging/central-channels'}
  context={'endpoint'}
></MethodEndpoint>

Create a channel in the central database

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails parameters={undefined}></ParamsDetails>

<RequestSchema
  title={'Body'}
  body={{
    required: true,
    content: {
      'application/json': {
        schema: {
          type: 'object',
          required: ['name', 'serverId'],
          properties: {
            name: { type: 'string' },
            serverId: { type: 'string', format: 'uuid' },
            description: { type: 'string' },
            type: { type: 'string', enum: ['text', 'voice', 'dm', 'group'] },
          },
        },
      },
    },
  }}
></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '201': {
      description: 'Central channel created successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              data: {
                type: 'object',
                properties: {
                  channel: {
                    type: 'object',
                    properties: {
                      id: {
                        type: 'string',
                        format: 'uuid',
                        description: 'Unique identifier for the channel',
                      },
                      name: { type: 'string', description: 'Name of the channel' },
                      serverId: {
                        type: 'string',
                        format: 'uuid',
                        description: 'ID of the server this channel belongs to',
                      },
                      type: {
                        type: 'string',
                        enum: ['text', 'voice', 'dm', 'group'],
                        description: 'Type of channel',
                      },
                      description: { type: 'string', description: 'Channel description' },
                      metadata: { type: 'object', description: 'Additional channel metadata' },
                    },
                    title: 'Channel',
                  },
                },
              },
            },
          },
        },
      },
    },
    '400': {
      description: 'Invalid request',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '500': {
      description: 'Error creating channel',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
