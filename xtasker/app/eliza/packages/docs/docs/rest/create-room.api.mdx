---
id: create-room
title: 'Create a room'
description: 'Creates a new room for an agent'
sidebar_label: 'Create a room'
hide_title: true
hide_table_of_contents: true
api: eJztV0tvGzcQ/ivEnFpjZcnxI+3e3FgFBLR1YDvoIfZhRM5KTLjkhuRKVgT992LI1SNWHoKR5hRdlqud+eb94BIiTgKUb8E7VwcoACdkY4CHAhQF6XUTtbNQwitPGCkIFJbmgqlF5bxAKxIHFOAa8sjUIwUlyER/41wNBTTosaZInkUtwWJNUGZRIwUFaJbQYJxCAZ4+tNqTgjL6lgoIcko1QrmEuGiYLUSv7QQKqJyvMUIJbasVrJ5qPLoSrhJxSp2Gq4cMTiH+4dSCEZ/Kks5GJi2XgE1jtEz29N8FBlzuq+LG70iy7Y1n66OmwF+zffsKf6rfP1jTWkP2J1swd94o9t+3rP2ysQmCsRjzOVDX6YBGyDZEV+dYj64YkmzUcfEc0GHiFKMrEZ1ApfixNTz9ODqhcTZkJ74YnPDjUxjOJ5FTS4nQSkkhVK0xC/huwetQdwjHzhlCCwXQI9aNoZwunHAYD0DUz/DXG6s/tCS0YpdXmnyqtt1UeX6SBdd6eQDvbaL7v1JUxKkOObnGZJydBBHdJsk613US0HvkEOtIdfg+Dv+iB1MmfiZ5NQWh7VZrYLqoI6dDystNGp8NBvuZO7IzNFrlXsTl9GMTtkITOGPJe+e/jSmdOiBBhgwmEu2qgJpCwMnBbGvy5O2I2oRvc14lQlIimSG0zTHlj9n163gkEbAOx9l+OC5TGKyLonKtVT+j8aOicf654uhU576u7SSX18+I/JCIJMXi1PHO1riQTOVFrIQ+Nrqf18H+stvVVv31nhjIz9b7XOsNlDCNsSn7feMkmqkLsTwdDAZ7Kv7Fn4WiGRnX1FyEGSmtZxzam+2KNlxHZ7MybuzeTCE4rfC38+rirHf+8uRl7+z84kVvfFrJ3gv5+8VpdXGBFV7Adhs6lH676hzGseIttnIpRmsnG/0RxfWtuHw92vPD5euRUE627IIUoTTgNyyzk+PB8clA9MSlqAw96rEhgVaJINEgv1yOullS8W49d/798b29t3c8nxhcB6Eo6ImltGyNSbSBlJjrOBUoUpTMQvjWWi65LFjbENFKOhZDqxqnbQwCjXHzpNy6Pot7W6PFCR+TTtpG8ihT7Sb8tW5BxKl37YQl3gxv7zJlhZI6XWmtqvMTtPojJbC8GlaOJTOmcjVqG8p72xNHR7eLEKk+OipFPvXmWpHY3D5CUonsTHtnU4IlZYmPGSD1/sAAeQoYXZFcyM7BW+odzMz4N9XOL7aMdXpPXGmPeSro71TC2k6YJb+QmKJVJrlOTtFaMiE7sSuoTsVWaZcE8UE03nELY18k0oZITkVY2DiloDfaKY3M86c2JNrGOFTZIv6wp9zdcMjEd74NvEwPH0m2KQ+HO6576oF/aXzr5HuKzHpDaHpR1ySkq+vWdg1azDSKTHU8uoYC2Kyc9Dmpu9aOMu5U9ibzX2WsyPvetrGEst+f6Dhtx8fS1X1iahfyM21i3LtqtDuI+cYqcD1OPqm/5Xa6HHC17bpupMfYbwxqy+WelFt23fItYKO31+cCyu3tNrfMhwK4KzLpcjnGQG+8Wa347w8t+QWUbx8KmKHXXN2prSod+Ky66fQVC3656S6yv4qnt94vKL9erC27eYam5Tco4D0tdm7mq4dVAVNCRT5plL++ynJ7d4yx5d6b0atizXEpJTXxq7QPO3Po9fXtHRQw7m7pdRq24HHOzsR51tQlP6ShmP5bgkE7adOEhYzJv/8AcNHLDw==
sidebar_class_name: 'post api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'Create a room'}></Heading>

<MethodEndpoint
  method={'post'}
  path={'/api/agents/{agentId}/rooms'}
  context={'endpoint'}
></MethodEndpoint>

Creates a new room for an agent

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails
  parameters={[
    {
      name: 'agentId',
      in: 'path',
      required: true,
      schema: { type: 'string', format: 'uuid' },
      description: 'ID of the agent',
    },
  ]}
></ParamsDetails>

<RequestSchema
  title={'Body'}
  body={{
    required: true,
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            name: { type: 'string', description: 'Name of the room' },
            worldId: { type: 'string', format: 'uuid', description: 'ID of the world' },
            roomId: { type: 'string', format: 'uuid', description: 'Optional custom room ID' },
            entityId: {
              type: 'string',
              format: 'uuid',
              description: 'Entity ID to add to the room',
            },
          },
        },
      },
    },
  }}
></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '201': {
      description: 'Room created successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              data: {
                type: 'object',
                properties: {
                  id: {
                    type: 'string',
                    format: 'uuid',
                    description: 'Unique identifier for the room',
                  },
                  name: { type: 'string', description: 'Name of the room' },
                  source: { type: 'string', description: 'Source of the room' },
                  worldId: {
                    type: 'string',
                    format: 'uuid',
                    description: 'ID of the world this room belongs to',
                  },
                  entities: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        id: { type: 'string', format: 'uuid' },
                        name: { type: 'string' },
                      },
                    },
                    description: 'Entities in this room',
                  },
                },
                title: 'Room',
              },
            },
          },
        },
      },
    },
    '400': {
      description: 'Invalid agent ID',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '404': {
      description: 'Agent not found',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '500': {
      description: 'Error creating room',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
