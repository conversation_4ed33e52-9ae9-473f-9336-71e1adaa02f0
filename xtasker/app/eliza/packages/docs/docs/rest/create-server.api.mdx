---
id: create-server
title: 'Create server'
description: 'Create a new server'
sidebar_label: 'Create server'
hide_title: true
hide_table_of_contents: true
api: eJztV0tvGzcQ/iuDORprS27THvampipgoGmC2EEPsg8jcqRlzCU3JFeyIui/F8PdlRTJTg0UyKm6iI95fPPk7BYTLSOWM6w5Rloat8SHAjVHFUyTjHdY4tvAlBgIHK8hclhxwAJ9w4GE4kZjiSrT3A6Xgb+0HNNvXm+w3OatCayxTKHlApV3iV2SK2oaa1QWNPocRd8Wo6q4JlmlTcNYop9/ZpV6uZ2gGTqqWcA2QaAkw1E48umBM6YgNu1ObHrmvuZEmtIzanf5J8pj413s9Pw0vpa/bz3V2Q+dMzTEVimOcdFau8H/YPW3FvZSjwjn3lsml6181oIzEV2c/pXO6HNXFbjwoaaEJbat0XiaLZ+c+dIyGM0umYXhAAsfIFU8pM6ueCFIp5L+oprBL054vxvHUxG/H3bnkl6O+KmYidZGlmR7dtjz7oo+9/UkHQkyLvEyl8LeW8alX988564nSKbmmKhuYF2xO4IJa4pDOqHkYDLJ8j7T+tSUizfj8Xk+3rgVWaOhL8cfkYMF8hPVjYBckI28K5BD8K9INuX1K5JiKsIg0+YQStd6NdtAntMokbHxNSkkhKwhmwHGdQGVy87zQ0iyiv7sl+ei0UOXaBq3PHTS/2PyA2LS1Xvl5bFqfK6FhlKFJY6oMaP98zfqwhKxwGFVzrbYBoslVik15WhkvSJb+ZjKn8fj8Rm6P+UaNK/Y+qZml/Y956FAierHw+s4HQIzvFwvmbw/PupaYpTYnh02WGzNV4L3tzD5cHPexz7cgPaqFVDZXbk371lW11fjq+sxXMIEFpafzNwykNMQFVmSzeQGaCkWLQLVvPbh8ere3bu7ykRRCCaC5miWjjUkD3OGNrKGtUkVEGS/2Q2E1jmpgE6xcTGRU3wFU6cbb1yKQNb6dQY3lEtx72pyOURFxiQdNpDKpZTlD9gipCr4dikaP05v7zrKBSnusfIA1YclOfOVszCf2+7Ci2aRqX1NxsXy3l3CxcXtJiauLy5K6FaXa6MZ9jNQzJDYrUzwLoc8g2VZdgImGZoIyCuwZsFqo3oHH6iPZHaM77j2YXNgrPM+cwXv6zNF74ZMFpZuw1CR0za7TlXkHNvYObFP8R5iq43PimQBTfDST8QXmbRhVhXEjUsVR7NHpw0Jzx/GMrSN9aQ7i+TiDNzddCrEd6GNMiBNn1i1OQ+nR6479cDfPL/16pGTsH5kspfyXoLydd26vlvCyhB0VFc377FAMatL+i6p+z5LKh3V2j7z33aykoxph1KP5Wi0NKlq51fK1yMWah+7//waSyOpyR1J7GflfW8/mVb2rf6Fobpve4mf0qixZPJMlwFt+3Y1Q2oMFkfz+qFRPRQoXUmItts5Rf4U7G4nx19aDhssZw8FrigYqWXZ7QqsmLSMgrMtPvJGgHUQL+8EipDbViCdvUy7YuCYKMVN+i7tw1H3/fD+9g4LnPffBnV+YjDQWuZ7WmOJ+esiJ0D+eJCzLVpyyza/K9jJlN8//FByNQ==
sidebar_class_name: 'post api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'Create server'}></Heading>

<MethodEndpoint
  method={'post'}
  path={'/api/messaging/servers'}
  context={'endpoint'}
></MethodEndpoint>

Create a new server

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails parameters={undefined}></ParamsDetails>

<RequestSchema
  title={'Body'}
  body={{
    required: true,
    content: {
      'application/json': {
        schema: {
          type: 'object',
          required: ['name'],
          properties: {
            name: { type: 'string' },
            description: { type: 'string' },
            metadata: { type: 'object' },
          },
        },
      },
    },
  }}
></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '201': {
      description: 'Server created successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              data: {
                type: 'object',
                properties: {
                  server: {
                    type: 'object',
                    properties: {
                      id: {
                        type: 'string',
                        format: 'uuid',
                        description: 'Unique identifier for the server',
                      },
                      name: { type: 'string', description: 'Name of the server' },
                      description: { type: 'string', description: 'Description of the server' },
                      metadata: { type: 'object', description: 'Additional server metadata' },
                      createdAt: {
                        type: 'integer',
                        format: 'int64',
                        description: 'Unix timestamp when the server was created',
                      },
                    },
                    title: 'Server',
                  },
                },
              },
            },
          },
        },
      },
    },
    '400': {
      description: 'Invalid request',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '500': {
      description: 'Error creating server',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
