---
id: delete-room
title: 'Delete a room'
description: 'Deletes a specific room'
sidebar_label: 'Delete a room'
hide_title: true
hide_table_of_contents: true
api: eJztVktv4zYQ/ivEnNpAsbxtetHNaFzAwC52kaToIclhTI4sbihSS1JOvIb+ezGk/Eic7hYtkNOeRIrz+GaG/Ga2EHEVoLoF71wboABckY0B7gtQFKTXXdTOQgWXZChSEChCR1LXWgpWgQJcRx5ZaqGgApXkrvJRhx5biuTZxRYstgRVdrFQUIBmyx3GBgrw9KXXnhRU0fdUQJANtQjVFuKmY7UQvbYrKKB2vsUIFfS9VjC8RLq4FK4WsSGRHMFQ7D0z4rdwzH5EdCInA4Z79hI6ZwMFNvzL9II/z9U5Z6OGEqGXkkKoe2M27OliOj3VWNg1Gq1ynILd++x6cQkFSGcjx19tAbvOaJmKVH4OrLs9DdMtP5OMXDXPJY06Yx2RHAkunTOEFgqgJ2w7Q1DVaAINBZD3zn/fpnSKXkvw8/DmbEwk2aGAlkLA1b9W24mnKkXUJnxf8zIJkhIpDKFtLjgfDsMwFBB15Gizi/HfxWu1nKWK7MphXRS16636UZW3qspvr72XjCE9MW1XO/76UZE3qEgCFht3aBGpPcQGKiix02XuO+V2bA5DmRpSuc2UPUABgfx610l6b6CCJsauKkvjJJrGhVj9Op1OT9C+52OhaE3GdS2/y2wp0TJjTwHvEBv9FcXHazH7tDixNPu0EMrJno2kcEXtvNirrN9NppN3U3EuZqI29KSXhgRaJYJEg7yZLUaurrkvPjr/MLmzd/am0YEdCh2EoqBXlhS3jyWJPpASjzo2AkWK02yE763l+5sdaxsiWkkTMbeqc9rGINAY95jASU/Il724sy1aXPEyYdI2kkeZHkKyv8MWRGy861fs8Wp+fZMla5Q0YqUdVOdXaPVXSsZcany1Y89sU7kWtQ3VnT0XZ2fXmxCpPTurRF6dP2pFYj85hASJ7Fp7Z1OJEljiZTaQCDWwgUytRtckN3JM8EH6yGZW/ECt85uDYpv2SStx80tHH9J70HbFKnlDokGrTEqdbNBaMiEncbySI8ReaZcc8UJ03jEfcC6SaEckGxE2NjYU9B6d0sg6f2hDou+MQ5Uj4oMTcDfzOQvf+D7wkDB/Itmnezg/St3LDPxFy2snHyiy6hWhOY+6JSFd2/Z2ZDux1iiy1GTxEQrgsPKlz5d65EmUiSfHcWp/83/PtuIGiqOnGaqyXOnY9MuJdG1JLO1C/gIzQudCbNEeWcxTpsAdNz97f9sDVX9jHB2pK9JTLDuD2jK1JVDbkXBuATt9GHULqA4T6W4MrsZR8b4AZhZW2m6XGOhPb4aBf3/pyW+gur0vYI1e8/tO1KR04LUayf4bMfx0NU6eP4uXM+s/hDH+RMuJXqPpeQcFPNDmaK7mgfd/ongxwP4nOGMGh/uhgIZQkU/5yYczKamLR2onrZfZed8yLufv5zdzGIa/Ae/vYPo=
sidebar_class_name: 'delete api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'Delete a room'}></Heading>

<MethodEndpoint
  method={'delete'}
  path={'/api/agents/{agentId}/rooms/{roomId}'}
  context={'endpoint'}
></MethodEndpoint>

Deletes a specific room

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails
  parameters={[
    {
      name: 'agentId',
      in: 'path',
      required: true,
      schema: { type: 'string', format: 'uuid' },
      description: 'ID of the agent',
    },
    {
      name: 'roomId',
      in: 'path',
      required: true,
      schema: { type: 'string', format: 'uuid' },
      description: 'ID of the room to delete',
    },
  ]}
></ParamsDetails>

<RequestSchema title={'Body'} body={undefined}></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '204': { description: 'Room deleted successfully' },
    '400': {
      description: 'Invalid agent ID or room ID',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '404': {
      description: 'Agent or room not found',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '500': {
      description: 'Error deleting room',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
