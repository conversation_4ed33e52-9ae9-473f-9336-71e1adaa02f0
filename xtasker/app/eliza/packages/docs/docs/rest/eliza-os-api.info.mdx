---
id: eliza-os-api
title: 'Eliza OS API'
description: 'API documentation for Eliza OS v1.0.10 - A flexible and scalable AI agent framework.'
sidebar_label: Introduction
sidebar_position: 0
hide_title: true
custom_edit_url: null
---

import Api<PERSON>ogo from '@theme/ApiLogo';
import Heading from '@theme/Heading';
import SchemaTabs from '@theme/SchemaTabs';
import TabItem from '@theme/TabItem';
import Export from '@theme/ApiExplorer/Export';

<span
  className={'theme-doc-version-badge badge badge--secondary'}
  children={'Version: 1.0.10'}
></span>

<Heading as={'h1'} className={'openapi__heading'} children={'Eliza OS API'}></Heading>

API documentation for Eliza OS v1.0.10 - A flexible and scalable AI agent framework.

This API is designed to be used with a locally running Eliza instance. Endpoints allow for creating,
managing, and interacting with AI agents through a REST interface.

The API is organized into the following domains:

- **System**: System-wide operations and environment management
- **Agents**: Agent lifecycle and management operations
- **Memory**: Agent memory and room management
- **Messaging**: Message handling, channels, and servers
- **Audio**: Audio processing and speech synthesis
- **Media**: File upload and media management
- **TEE**: Trusted Execution Environment operations
- **WebSocket**: Real-time communication via Socket.IO

<div
  style={{
    display: 'flex',
    flexDirection: 'column',
    marginBottom: 'var(--ifm-paragraph-margin-bottom)',
  }}
>
  <h3 style={{ marginBottom: '0.25rem' }}>Contact</h3>
  <span>Eliza OS Community:</span>
  <span>URL: [https://github.com/elizaos/eliza](https://github.com/elizaos/eliza)</span>
</div>
