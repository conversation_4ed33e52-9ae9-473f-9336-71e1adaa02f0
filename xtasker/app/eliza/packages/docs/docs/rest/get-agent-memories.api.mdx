---
id: get-agent-memories
title: 'Get agent memories'
description: 'Retrieve all memories for a specific agent'
sidebar_label: 'Get agent memories'
hide_title: true
hide_table_of_contents: true
api: eJztWMFy2zYQ/ZUdXNp6aMtp0x5488RKRjN1k7GV6SH2AQJWJGIQYABQtqLRv3cWACXGUmwlnuYUXUSJ2PcesIvdBVYs8Mqz8gNrsLFuyW4KJtELp9qgrGElu8TgFC4QuNYQByn0MLcOOPgWhZorAbxCE1jBbIuOk+FEspJVGM7oxUW2YgVrueMNBnTEuWKGN8hKFs0nkhVMEWXLQ80K5vBTpxxKVgbXYcG8qLHhrFyxsGzJzAenTMUKNreu4YGVrOuUZOuHU5icg51DqDHrXBcb5sBnGv+h58z9qUO3ZI+RSZzzThNbg97zCv0u45RggTggWEiYA1ZlhO4kjpsZSplQHyefWauRmyH7nGuPu1NNyIA9NCxQBOs8KAMOfWuNx6EUUXNjUG8X/+kFeGK1Xysd0MFsCRkbJudDRmdt87/QEXDkuqHYSVP1hPj76Sl9fWkXI3Mb0C6HuQTfCYHezzutSZiwJlDQlCvG21YrEeN79NETympXt519REF7oXW0G4JKGjLqXp/iPW9ajSnOaYI8HIDYSx+M5M5x0qwCNv5pBCWfXu+Hy/3eqE8dgpJ<PERSON>pordDEV0N7KCWRdMHoVlpPvQN/u1IQB3HsrFA8o4U6FGkKt/ICpTxzPIIoQT/HkmH0GTQzOASrMUFtTeQiWCIRDIj8LAw5lAlbohiTKhL9e7vPJPQTVoA+8aeGuRjPwCNxxDxk/Um0D+vHwCHgf9ifAL1Id3gfImP1kc2IktlDbrqoPwIn78RfKUwGd4RqyJbTOxp2zLliruTkciUaDdeCQe2toJEWMoFGHbJqMvpNwzhLCIHjuuAnkSAj8Nk7a284JfFrpVRy3Z9WUucRWL6f2OSGX8VLUqVwBWr2MIUeuUYGSDnuVA2L430UO/f5TsJf7kujELLhWMq/D5PwH58u+BqJz1j2NKaw8wCljAoM4dl30Nf5Qs4EPJQau9J6Iemh5HgeihDgNUCY5mF6mpe+dEilY746XX6tpxgaY287In974Ud74c9/mSBpyY0GtWLPtg3/65Qf4JQoLtc1nkXj8CDUr2Yi3apSq42iVe4j1aOAej27Rn1E6p1nJ6hDacjTSVnBdWx/KP05PT3dE/k2vQeICtW0b2owJKfakJDnOsxeq1WcOb6/g7N1kt4i9m4C0oiOQOMvYZ21MFi9OTk9enMIxnMFc472i8wY3ErzgOh4+ziY5K8/pxHVn3e3Jtbk2UyoGBK48SPSqMiipdM0QOt93PxziPPUSXGeocmZiZXzgRuAJjI1sraKyx7W2d1Fc7DGUqYpr03DDK3qMmmJJp8JrqoTfa6Mi6qjKA4fL8dU0jZxzgVkr9lKtq7hRnzGC2Vje5paYCVPahivjy2tzDEdHV0sfsDk6KiE9Hd8pibA5mfooCc1COWuii6JYpMcEELOoJ4CUT7Wao1iKvMDb0QPMZJiK5tYw919kFdu/h0QXcRsoU5HJRa7WNTdSx6XLxyefFjGHZJbYSWUjET30LRKtRRzaIooa/NKEGr3aqJOKk81rpRG6Vlsu04zoxY646XhMg6eu89QVj+9RdDEOx4Ole7gC/+LsyopbDGR6iVwfU1sKwjZNZ3KSg4XikEadTN6ygtG0UtCnoM7pkYuYHvOpcRP5rxJWoJZtuzV9ORpVKtTd7ETYZoQ02vr0HRub1vrQpO4xI77BkHfIYON/sQlX2zT9bdcgOZNRAz1qNVeGMl0Uu8op6APjrWJFf+tSsHJ7B7KRc1MwSjU0erWacY/vnV6v6e90aqbsJJWn7S5zmn9kBvvuJL4i9RaXD+5GFlx3NC4e5A/nPOw64lEVe+5KvlPM3ouJR7mHlyPPJu2vJx5l3FyObOlu6IdTxPeNHv/1Mt+g/QYPr8C+IqI/EpnlUEIvrg/R9c26YDVyiS4qSm/PhMA2DOx2GhuayqYgvxlP2Xr9HxI8Gak=
sidebar_class_name: 'get api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'Get agent memories'}></Heading>

<MethodEndpoint
  method={'get'}
  path={'/api/memory/{agentId}/memories'}
  context={'endpoint'}
></MethodEndpoint>

Retrieve all memories for a specific agent

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails
  parameters={[
    {
      name: 'agentId',
      in: 'path',
      required: true,
      schema: { type: 'string', format: 'uuid' },
      description: 'ID of the agent',
    },
    {
      name: 'tableName',
      in: 'query',
      schema: { type: 'string', default: 'messages' },
      description: 'Table name to query',
    },
    {
      name: 'includeEmbedding',
      in: 'query',
      schema: { type: 'boolean', default: false },
      description: 'Include embedding vectors in response',
    },
    {
      name: 'channelId',
      in: 'query',
      schema: { type: 'string', format: 'uuid' },
      description: 'Filter by channel ID',
    },
    {
      name: 'roomId',
      in: 'query',
      schema: { type: 'string', format: 'uuid' },
      description: 'Filter by room ID',
    },
  ]}
></ParamsDetails>

<RequestSchema title={'Body'} body={undefined}></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '200': {
      description: 'Agent memories retrieved successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              data: {
                type: 'object',
                properties: {
                  memories: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        id: {
                          type: 'string',
                          format: 'uuid',
                          description: 'Unique identifier for the memory',
                        },
                        entityId: {
                          type: 'string',
                          format: 'uuid',
                          description: 'ID of the entity associated with this memory',
                        },
                        agentId: {
                          type: 'string',
                          format: 'uuid',
                          description: 'ID of the agent associated with this memory',
                        },
                        roomId: {
                          type: 'string',
                          format: 'uuid',
                          description: 'ID of the room this memory belongs to',
                        },
                        createdAt: {
                          type: 'integer',
                          format: 'int64',
                          description: 'Unix timestamp when the memory was created',
                        },
                        content: {
                          type: 'object',
                          properties: {
                            text: { type: 'string', description: 'Text content of the message' },
                            thought: {
                              type: 'string',
                              description: "Agent's internal thought process",
                            },
                            plan: { type: 'string', description: "Agent's plan or reasoning" },
                            actions: {
                              type: 'array',
                              items: { type: 'string' },
                              description: 'Actions the agent wants to take',
                            },
                            source: { type: 'string', description: 'Source of the message' },
                            inReplyTo: {
                              type: 'string',
                              format: 'uuid',
                              description: 'ID of the message this is in reply to',
                            },
                          },
                          title: 'Content',
                        },
                      },
                      title: 'Memory',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    '400': {
      description: 'Invalid agent ID',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '404': {
      description: 'Agent not found',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '500': {
      description: 'Error retrieving memories',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
