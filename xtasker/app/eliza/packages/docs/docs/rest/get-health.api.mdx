---
id: get-health
title: 'Health check endpoint'
description: 'Detailed health check for the system'
sidebar_label: 'Health check endpoint'
hide_title: true
hide_table_of_contents: true
api: eJytVU1zGzcM/SscHD1rSWmml715WjXxtBlnYnV6sH2ASGiXMZdkSaxsRbP/vQPuypYj5+NQXUQugYcHEHjcA2OTob6BvMtMHdxVYCjrZCPb4KGG34nROjKqJXTcKt2SvlebkBS3pCavCkKkhOJyaaCGhvh9MYcKEuUYfKYM9R5+WSzk72WI6wKibJ5i7KACHTyTZzHGGJ3VBXz+OYvHHrJuqUNZ8S4S1BDWn0kzVBCTUGE7xsuM3Ocju8zJ+gYqoEfsopNPV3/CUMGWUrYj+EvToQK2HWXGLr4GtAmpQ4YaDDKdi6m4GIrkDXk9Efk+TWzI8w9oHmozTL8Kfl28/V4xe///l7PXmvIx0XUIjtAfM92gyzRUQCmF9GNMHQy9lvnLtJYCportUEFHOWPz024H83Iv0s6vlvobfV/SUNaP9yyHY/XZcrmXEgKmK+mI2zBNgGSK3EINc4x2niltKc3bw1yMe5m9PfTJyQ0zx3o+d0Gja0Pm+u1isTgh9pccK0NbciF25FmNSDDcVSA0S24Hcs5+QXV1rS4+Xp4gXXy8VCboXkBKZmWsn1y2b2aL2ZuFOlcXauPo0a4dKfRGZY0OZXNxqUrnqk3Cjh5Cup/d+lu/am2WgNKEhrJtPBnFQa1J9ZmMerDcKlQlT7dTqffe+mYKbH1m9JpmaulNDNZzVuhceCjkdCJk65vq1nfosZFl4WQ9U0ItZyP+gVtW3KbQNxLx0/J6NVpuUNPElQ5UQ2rQ2y9UwEKRt02QyIJpQofW5/rWn6uzs3HIzs5qNa7OH6wh9aSBuVAiv7Up+HJFhSzJcgS4KNQEoKyUsxvSOz0V+Nn6CHN0/EBdSLtnx67si1cKoTsJ9KG0vvWNuIwbUi1640rpdIvek8tjEaeWnCj2xoYSSBYqpiCjL7UoppFItyrvPLeU7RM7Y1F8/rCOVB9dQDNmJAcn5FbLpRivUp+ZjFo+ku5LHy6PSvd1Bf6h9XXQ98Ti+onQFc1VOnRd7ydhU1uLarSaXV7BkbrD2NSTJKIukuixezEsv41YLMr5PJq5ns8by22/nunQzUmsQx7/QYY/hswd+iPE98dvJk39/PUc7p/V+Wdf20m6mB55Hh1aL9JWmO4nzbkBjPZJZaCang9530VaxGC/X2Omv5MbBvn8b09pB/XNXQVbTFYGXHZDcTWUilDd006EQ2uKkscWXS9ETt4U0aInLXy3XMEw/AfoDOyz
sidebar_class_name: 'get api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'Health check endpoint'}></Heading>

<MethodEndpoint method={'get'} path={'/api/server/health'} context={'endpoint'}></MethodEndpoint>

Detailed health check for the system

<ParamsDetails parameters={undefined}></ParamsDetails>

<RequestSchema title={'Body'} body={undefined}></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '200': {
      description: 'System is healthy',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              status: { type: 'string', example: 'OK' },
              version: { type: 'string' },
              timestamp: { type: 'string', format: 'date-time' },
              dependencies: {
                type: 'object',
                properties: { agents: { type: 'string', example: 'healthy' } },
              },
            },
          },
        },
      },
    },
    '503': {
      description: 'System is unhealthy',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
