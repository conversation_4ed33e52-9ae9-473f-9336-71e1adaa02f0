---
id: get-room-memories
title: 'Get room memories'
description: 'Retrieves memories for a specific room'
sidebar_label: 'Get room memories'
hide_title: true
hide_table_of_contents: true
api: eJztWE1vGzcQ/SsEL22NtaW0SQ97MxI1ENA0he2gh9iHETnaZcwlNyRXtiLovxdDcqWNpciKA/gUw4BWWs57w/nicFY8QOV5+ZE32Fi35AV31jaeFxwqNMHzm4JL9MKpNihreMkvMDiFC/Qsiij0bG4dA+ZbFGquBCMEXnDbogMSmkpe8grDhbXNuyzDC96CgwYDOuJfcQMN8jLRTiUvuCK2FkJNSuHnTjmUvAyuw4J7UWMDvFzxsGxJzAenTMULPreugcBL3nVK8vVD7advmJ2zUCOLRHxdbJhJ7ecgjuYZ8ArbmdDTfu4wemGHR5mAFTpOuHPodODlq/EOyz9dM0NHTBvnBMtcdtmQtTPqc4eP0s6s1QhmSEuG2CG+wNA5w6zRS5agNxoMWX0Ad/xed1guSZwF1aAP0LRsrnSI6zYEaOTT4SdG7gG/oSjwrTUePaH8Ph7Tx4P9W9tsbd4bXDLfCYHezzutSRdhTaCwK1cc2lYrERNk9MkTyGpXVTv7hIIM1jpKp6CSChl1r5/wHppW49ZPEIaI4ByQJipg4x9nUvLxWH9oxg/J/0qiCWqu0MUCQaGfi8y64PQqLKdPQN8mUsJg4L0VCgJKdqdCzUKt/ICpLyg/QBQhHuPJ5eMHaAhhiMpmqK2pKIWJQDgk8vOwtypsSJQJf77c55P7QWzf1WgGHmF34FnGj1TbMD0cHgHvw74tf01+hfeBZcx+sw16DxWVJB5q21X1ETjn5IdfPKNdOwOaZUnWOhvzYV3wVoM5HolWM+uYQ/DW0EqKGEGr/BFJk9F3Csl5QhgEzx2YEGtxgNu4aW87J/BxTS/juj1WU+YCW728sj8SchkvRR39G+YINYYcuUYFKiX8dQ6I4W/vcuinv4K/3FcXp2YBWslsBSJ2Kc6nb565HM5Be6qH6Jx1j2MKK4/wzoTAWFy7LnjvnSPFBs6UGEDpPaH1UPJNXIiSxW0wZZKn6WXyQu+dSMF7z7zc9UxMgY07jA1sbrt4ev70yrN45dW+fEk65P5BmWrbRf30y7P4JSoWapvvLPGeEmpe8hG0apQuRaNVbirWo3hbGq3S4b8eDbzl0S36u03nNC95HUJbjkbaCtC19aH8Yzwe7+j8N71mEheobdtQjiak2IjSDuK2e721+gLs/SU7/3e6e8j9O2XSio5A4qZjH7YRWbw4G5+9GLNTds7mGu/VTCMDI5kXoIG+nE9z3Z7TTe3Outuza3NtruiwIHDlmUSvKoOSjrYZss733RGwuE+9ZK4zdLJmYmV8ACPwjE2MbK2iYxG0tndRudiDKFMV16YBAxU9Rp3ikU8Hs6kSfq8bHbKOugAG7GJyeZVWzkFg1hV7Va2rwKgvGMFsPP7mlpgJU9oGlPHltTllJyeXSx+wOTkpWXo6vVMS2eZC66NKaBbKWRNdFJVFekwAsbh6AkhlVqs5iqXIBt6uHmAmwXSobgVzf0ZSsU4/JHoXs0KZikTe5dO8BiN1NJ2owRjUPhkxh2RWsZPKRiJ66FsoskVc2iKKmvmlCTV6tdFOKiCZv5RG1rXagkw7ohc7yl1NJrT4ynWeuubJPYouxuFkYLqHFvgPZ5dW3GIg0QsEfUptKxO2aTqTax5bKGBp1dn0PS84bSsFfQrqXC1BxGqZ74abyH+dsAK1dNvU9OVoVKlQd7MzYZsR0mrr02dsfFrrQ5O6y4z4FkN2yzbvv8rB1bZoHz84yTWNeutRq0EZqnlRz1UuRh85tGo7oyl4uR2b9PObcjPP2Ch3U3CqOyS/Ws3A4wen12v6Od2VqVRJ5Sn3ZT4CDuznkVnDN/Zxi8vBzGMBuqM18Qp/PPWhacNB3s3U44nE3xhAHOTsZx5PpNw7lDhImGYgW7ob+uIU8X2ni3+9yOOv39jDwdk3NOgvTGY5VKHXrI/S77PAXi0O5cphJXJerG/WBa8RJLpolfTyXAhsh97a6bTInJsO4e3kiq/X/wPqWl2u
sidebar_class_name: 'get api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'Get room memories'}></Heading>

<MethodEndpoint
  method={'get'}
  path={'/api/agents/{agentId}/rooms/{roomId}/memories'}
  context={'endpoint'}
></MethodEndpoint>

Retrieves memories for a specific room

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails
  parameters={[
    {
      name: 'agentId',
      in: 'path',
      required: true,
      schema: { type: 'string', format: 'uuid' },
      description: 'ID of the agent',
    },
    {
      name: 'roomId',
      in: 'path',
      required: true,
      schema: { type: 'string', format: 'uuid' },
      description: 'ID of the room',
    },
    {
      name: 'count',
      in: 'query',
      schema: { type: 'integer', default: 50 },
      description: 'Number of memories to retrieve',
    },
    {
      name: 'unique',
      in: 'query',
      schema: { type: 'boolean', default: true },
      description: 'Return only unique memories',
    },
    {
      name: 'start',
      in: 'query',
      schema: { type: 'integer' },
      description: 'Start timestamp filter',
    },
    { name: 'end', in: 'query', schema: { type: 'integer' }, description: 'End timestamp filter' },
  ]}
></ParamsDetails>

<RequestSchema title={'Body'} body={undefined}></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '200': {
      description: 'Room memories retrieved successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              data: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: {
                      type: 'string',
                      format: 'uuid',
                      description: 'Unique identifier for the memory',
                    },
                    entityId: {
                      type: 'string',
                      format: 'uuid',
                      description: 'ID of the entity associated with this memory',
                    },
                    agentId: {
                      type: 'string',
                      format: 'uuid',
                      description: 'ID of the agent associated with this memory',
                    },
                    roomId: {
                      type: 'string',
                      format: 'uuid',
                      description: 'ID of the room this memory belongs to',
                    },
                    createdAt: {
                      type: 'integer',
                      format: 'int64',
                      description: 'Unix timestamp when the memory was created',
                    },
                    content: {
                      type: 'object',
                      properties: {
                        text: { type: 'string', description: 'Text content of the message' },
                        thought: {
                          type: 'string',
                          description: "Agent's internal thought process",
                        },
                        plan: { type: 'string', description: "Agent's plan or reasoning" },
                        actions: {
                          type: 'array',
                          items: { type: 'string' },
                          description: 'Actions the agent wants to take',
                        },
                        source: { type: 'string', description: 'Source of the message' },
                        inReplyTo: {
                          type: 'string',
                          format: 'uuid',
                          description: 'ID of the message this is in reply to',
                        },
                      },
                      title: 'Content',
                    },
                  },
                  title: 'Memory',
                },
              },
            },
          },
        },
      },
    },
    '400': {
      description: 'Invalid agent ID or room ID',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '404': {
      description: 'Agent or room not found',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '500': {
      description: 'Error retrieving memories',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
