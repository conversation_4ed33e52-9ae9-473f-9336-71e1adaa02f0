---
id: get-server-agents
title: 'Get server agents'
description: 'Get all agents for a server'
sidebar_label: 'Get server agents'
hide_title: true
hide_table_of_contents: true
api: eJztV01z2zYQ/Ss7e2o9tKW06YU3T6JmNNM0mdiZHmwfVsBKRAwCNADKVjT8750FKFmO3DqnnKILQWE/HnYXb5dbTLSKWF9hyzHSyrgV3lSoOapgumS8wxrfcQKyFmjFLkVY+gAEkcOaA1boOw4kknONNa44XeSd8yyMFXYUqOXEQdxs0VHLWGNRn2us0IiPjlKDFQa+601gjXUKPVcYVcMtYb3FtOmyXgqCscKlDy0lrLHvjcZhuBHl2HkXOYr8b9OpPJ6epICCwCkYXrOG2CvFMS57azdYofIusUuiSF1njcoHm3yJor09RuMXX1glOWSQMCRTfI9WDwQX3lsmh0OFmtKhCQqBxLVJ3MaXTRv9cjC+zd9nZ+56BqPZJbM0HHIKU8MlowKqZOXY8FNDf1PL4JdPVWOi1MfnlNn1rZQWqWTWnDM9Lo9K7E0fArsExdg3PoYKk0mWdwmcu6XHofwqfP1couduTdbosUph/vZHJLdCfqC2E6BLspGHCjkEH162qbz+jvDPxBhk2aEaL+x3q+3EpQI5kbHPpuyp5tssyBryMcC4UmeyWWK/S0t2gbt8vD7OR+EEcD7B0vdO/0zHj0rHH89dj4JhpEHjViO1/8zKj8lKBpYaPzbM3CRTgzVOqDOTfSeeFPaKk+2uWw6TfaLGvdxT+2Cxxialrp5MrFdkGx9T/ft0Oj2C+5dsg+Y1W9+1mXNLJ5cOKuDziXeQrflK8OECzj/Ojyydf5yD9qoXI/m8ua3sVdavzqZnr6ZwCuewtPxgFpaBnIaoyJK8nM9L3cFSJoR7H27Prt21u2xMFIdgImiOZuVYQ/KwYOgja7g3qQGCfE67gdA7JyVcHBsXEznFZzBzuvNG2j1Z6+8zOBWYknGr6tq15HKQq4zJuMRBmpNbFfs7bBFSE3y/Eo+fZheXRXJJikesvIPqw4qc+crZmM/9a+nFs9jUviXjYn3tTuHk5GITE7cnJzWU1em90Qz7QSpmSOzWJniXU5TBsiyLgTLHiIG8AmuWrDZqDPCj9IHNovieWx82j4ptfs9awfv2yNH7XS2KSnlhaMhpm0OnGnKObSxBHEtyhNhr47MjWUAXvBBC5hoR7ZhVA3HjUsPR7NFpQ6Lzp7EMfWc96XIi2TgCdzmbifBl6GNiDbMHVn2uw9lB6L6NwD+8uPDqlpOofmKyp8m0DMq3be9GuoO1IShSZ/MPWKEcqxR9KeqRKEllohxH2n3lvym2kox1j1cz1pPJyqSmX5wp305YpH0szzzjdD6mltyBRRm8xwlmf++f3MHtI1+/MKWPHJb4IU06SyaPohncdiSfK6TOYHXwIfBIMhXWBwP7iOWmQqEZ0dxuFxT5c7DDIH/f9Rw2WF/dVLimYOSyZ57SJspaj9T/P4f55dP4IfAr/Bf43fzsJMxrsr28YYW3vDn8vhhuhgobJs0hYyjb50pxlw4Uj5qd0OGepN/NLnEY/gV7GpN0
sidebar_class_name: 'get api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'Get server agents'}></Heading>

<MethodEndpoint
  method={'get'}
  path={'/api/messaging/servers/{serverId}/agents'}
  context={'endpoint'}
></MethodEndpoint>

Get all agents for a server

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails
  parameters={[
    { name: 'serverId', in: 'path', required: true, schema: { type: 'string', format: 'uuid' } },
  ]}
></ParamsDetails>

<RequestSchema title={'Body'} body={undefined}></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '200': {
      description: 'Agents retrieved successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              data: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: {
                      type: 'string',
                      format: 'uuid',
                      description: 'Unique identifier for the agent',
                    },
                    name: { type: 'string', description: 'Name of the agent' },
                    status: {
                      type: 'string',
                      enum: ['active', 'inactive'],
                      description: 'Current status of the agent',
                    },
                  },
                  title: 'AgentInfo',
                },
              },
            },
          },
        },
      },
    },
    '400': {
      description: 'Invalid server ID',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '404': {
      description: 'Server not found',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '500': {
      description: 'Error retrieving agents',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
