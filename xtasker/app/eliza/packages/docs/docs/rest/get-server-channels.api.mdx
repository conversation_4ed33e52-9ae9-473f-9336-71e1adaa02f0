---
id: get-server-channels
title: 'Get server channels'
description: 'Get all channels for a server'
sidebar_label: 'Get server channels'
hide_title: true
hide_table_of_contents: true
api: eJy9Vk1z2zYQ/Ss7e2o9tKW0OfHmSdSMZpomEzvTg+3DCliRSECAAUDZiob/PbMgKdPWtE4v9cWgsPv27QcecMBEVcTyBhuOkSrjKrwrUHNUwbTJeIclvuMEZC2ompxjG2HrAxBEDjsOWKBvOZDYrjWWWHG6yjtvRnMssKVADScOEuqAjhrGEgeAtcYCjcRpKdVYYOBvnQmssUyh4wKjqrkhLA+Y9m32S0F4Frj1oaGEJXad0dj3d+IcW+8iR7H/bbmUf0+zmWhB4BQM71hD7JTiGLedtXssUHmX2CVxpba1RuXkFl+i+B9O+fjNF1ZJ0gxSimSG6CPqzHDjvWVy2BeoKc0hKASS0CZxE1+GNvrlcjzv4mdnvnUMRrNLZms45Dammqe+Cq2hM6fQT6H+oobBb587H/v5n7mt305wAwak2sQJGjZsvasiJC9RBuTTCOy6RgY58YMUbOeNYgnUYIFV8F17OtjX+zbnMcvhicFLdRhHCea/9gU2nOhZf499fApwqbWRJR0PFxyde8nVJMuPgbAf/gp8vXx9OtnDsQPnE2x95/T/McoF8gM1rbDcko3cF8gh+PAypvL6J0ZtJWCQbXNlRaR+2m0yz31NZGx82fNtNmQNOQ0wbpjc3Np+3pMcAsd+NJxqP6pf1rtUY4kLas3iKKyLYbbj4jAdlH6hHjVy3M0C2QWLJdYpteViYb0iW/uYyt+Xy+UJ4T9lGzTv2Pq2YZcmYRY5FPo554m0Nd8JPlzB5cf16TR+XIP2qhOQnHFWiKPL7tXF8uLVEs7hEraWH8zGMpDTEBVZko/LNVAlDLYi9/c+fL24dbfuWg6zgJsoR8VUjjUkDxuGLrKGe5NqIMh52j2EzjnjqjGwcTGRU3wBK6dbb1yKchn5+0xOBaZkXFXcuoZcLnORORmXOJCSvQF/4hYh1cF3lUT8tLq6Hiy3pHjkyhNVHypy5jtnMJ/VaeslsmBq35Bxsbx153B2drWPiZuzsxKG1fm90QzHezFmSux2JniXW5TJsiwHgMtMTQDyCqzZstqrscCP1jPMwfE9Nz7sHx2b/J29gvfNSaD30zSKy/DBUJPTNpdumsehiONIjhQ7bXwOJAtogxdJkFpk05ZZ1RD3LtUczZGdNiQ+fxjL0LXWkx4yko0TcterlRhfhy4m1rB6YNXlOVzNSve8An/z5sqrr5zE9ROTPU+mYVC+aTo3Ch7sDMFgdbH+ILcDhzgM/TDUo1SSylI5vk+Ok/9mwEpyQz8ezVguFpVJdbe5UL5ZsFj7OPzP6t36mBpyM0R5SY033OzkP7tzjpr94sNrVDK58RatJZNvn0zwMErQDVJrsJi97h6FpsBy9gI78rkrUMRGfA+HDUX+HGzfy8/fOg57LG/uCtxRMHLks1ppE2WtxyvgXxL65dP4tvsV/on+9CByUuwd2U6+sMCvvJ8/Gfu7vsCaSXPIHIbtS6W4TTPHk0tPRPEo1u9W19j3PwCquNGO
sidebar_class_name: 'get api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'Get server channels'}></Heading>

<MethodEndpoint
  method={'get'}
  path={'/api/messaging/servers/{serverId}/channels'}
  context={'endpoint'}
></MethodEndpoint>

Get all channels for a server

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails
  parameters={[
    { name: 'serverId', in: 'path', required: true, schema: { type: 'string', format: 'uuid' } },
  ]}
></ParamsDetails>

<RequestSchema title={'Body'} body={undefined}></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '200': {
      description: 'Channels retrieved successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              data: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: {
                      type: 'string',
                      format: 'uuid',
                      description: 'Unique identifier for the channel',
                    },
                    name: { type: 'string', description: 'Name of the channel' },
                    serverId: {
                      type: 'string',
                      format: 'uuid',
                      description: 'ID of the server this channel belongs to',
                    },
                    type: {
                      type: 'string',
                      enum: ['text', 'voice', 'dm', 'group'],
                      description: 'Type of channel',
                    },
                    description: { type: 'string', description: 'Channel description' },
                    metadata: { type: 'object', description: 'Additional channel metadata' },
                  },
                  title: 'Channel',
                },
              },
            },
          },
        },
      },
    },
    '404': {
      description: 'Server not found',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
