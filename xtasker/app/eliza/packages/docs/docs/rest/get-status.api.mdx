---
id: get-status
title: 'Get system status'
description: 'Returns the current status of the system with agent count and timestamp'
sidebar_label: 'Get system status'
hide_title: true
hide_table_of_contents: true
api: eJytVU1v4zgM/SsCj4UbZ3ZvvhVFZhBgZ2fQZLGHtgdGZmxN9eGV6LSZIP99QclN0skeNxdTNvn4SJEvB2DsEjSPkPaJycFzBS0lHc3AJnho4IF4jD4p7knpMUbyrBIjj0mFbX5bItWr4V5hJ991GD0r9K1i4ygxugEqCANFFNRlCw10xKsMAxVESkPwiRI0B/htPpfHRxarkmNKbPw2RJexoAIdPJNnCcJhsEbnD/WPJJEHSLonh2LxfiBoIGx+kGaoYIhCiU3JW7Av/BJH4zuogN7QDTaHvsCxglzkvdR44W08U0cRfu3fn6PbUJReoWazo9KiJDjn5vxH0lIhNNAi0624XkHfT9dxxjmWXwWOuA9Tm6VS5B4aqHEwdaK4o1in9+aXs8zAAcZooYGeeWjq2gaNtg+Jm9/n8/lV9j/ks2ppRzYMLo9FRoLjcwVyQ7kqw7lxC2t+ovq2Unffl1dId9+Xqg16FJB8d2obojqF7D7N5rNPc3Wr7tTW0pvZWMrDlTRalMPdchq8bURHryG+zJ78k1/3JklCZZJqKZnOU6s4qA2pMVE7TazKddq9iqP3xndTYuMTo9c0UwvfDsF4TgqtDa+ZnI6EbHxXPXmHHjsxMycZgyg37buC/85NNiiGsZOMD4vVunhuUdPEld6phtihNz8pg4W8YtsgmQWzDQ6NT82Tv1U3N2Utbm4aVazbV9OSOi1aypTI70wMPl9RJktiFoC7TE0AsqWs2ZLe66nBZ+8LzBL4lVyI+3Ogy+ccFUNwV4m+Ukq5TRJSDqR69K3NrdM9ek82lSZOIzlRHFsTciIx1BCDppSkF9l1INK9SnvPPSVzYtcalJjPxpIaBxuwLRXJhyty68VCnNdxTEytWryRHvMcLi5a92sH/qbNKugXYgl9ILR5SZUOzo1+EiG1M6iK12z5DSqQssrQl6Ge5At1lhKP7sOy3Bcs3kN1sZqpqevOcD9uZjq4msQ7pPIEWf4hJHboLxC/EL/L9GnvP+zg4ayi/6PiT5rG9Mb1YNF4Ub1cyGGSpEfAwZxESIxC77kCUR5xOBw2mOivaI9Hef3PSHEPzeNzBTuMRvZfTscKesKWYtaxF9qLrmhNg8jfDu0oRK7+HkSqTlL5ZbGG4/FfdCB4qg==
sidebar_class_name: 'get api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'Get system status'}></Heading>

<MethodEndpoint method={'get'} path={'/api/server/status'} context={'endpoint'}></MethodEndpoint>

Returns the current status of the system with agent count and timestamp

<ParamsDetails parameters={undefined}></ParamsDetails>

<RequestSchema title={'Body'} body={undefined}></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '200': {
      description: 'System status information',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              status: { type: 'string', example: 'ok' },
              agentCount: { type: 'integer', description: 'Number of active agents' },
              timestamp: { type: 'string', format: 'date-time', description: 'Current timestamp' },
            },
          },
        },
      },
    },
  }}
></StatusCodes>
