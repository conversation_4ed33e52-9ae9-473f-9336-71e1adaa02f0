---
id: ingest-external-message
title: 'Ingest external message'
description: 'Ingest a message from an external source for agent processing'
sidebar_label: 'Ingest external message'
hide_title: true
hide_table_of_contents: true
api: eJztWE1v2zgQ/SsET1sEqp2Pu6ubd7sFDGzQok2xhyaHMTmy2FCkSlJ2vIb/ezGkZKWx06QI0tMGlkTNvHl8M0PNIhUpI1UuFOjcBA7nJOfoKqQOKGd43p9p7SxnKCU9s4IlTkOkllYY4Mh+QIVAg1hTx2BCShLZxLgKzYnHRtUuZOjzypyEfA8W3SHkqOvpiyB5KJE7S01IffPPpBKmZJsAY6BlimVqNCDN0Zbgd5WB62OBZjiQVgsTrSnHSteknus9Ka9n1RHJyFojCky15NC+4vRTQhk3BvFJTr++9a6vk3WYRQP8AJ8kARJcyIzOaHrgK6yOeRrcDVSbhNG8foUAC8M02YvDWBTzGTHWcLS3uosuhN47d3uOYXIMx1ZwwTNdgo6YI7Gk9c4FlID46s0RNg1mxL0BxyblxtI/3pASo9EzVZiSBE+QpiYUSK10Up1a0YLE17OTi9PJJPXnN5P5LLynfBHg4XxGr+fh3ocYD+PNIaRrvMK7qX+Ln5LZbkJ+Tt/cT9vdQBNP2MRNVuX5k19YZzc9qbZG7L98njGwySjDqIOl2ggLESZW/hWCyjjxEZ5IaQk/jMajcXAHxUamLfvJTK6N4//j8O9PXB/6h3DhnGH0To5u7jAXUkqhhNi0OMU3Zs6hhUZS/FoqS+qZclgqWoCenAP5voXaAdR9ODnUZd1jMqUOk7Q0hXY+daciAtRKOTEsi10xCUncPCbhSXRy8Wo0HU2Si5/CuOkvG0Sp11pd9DyTM+7ybTqAPc8qjiSSDCXtVhM5SmhDrstnx8cszs6Jy7Be/9lcKlE3FHLEnWMRjHwnTPZDUTdDeRB73qPJKMY4SSXGSMIR7xCyLZbGNStC08SjGUViFlkkeoVZYfyxqVTJglShHp8+l8klsia8LvXzRUkoI4PrlGKReNYVOMFKPNbJ/2L+JAvqjKJNPLGCYu5p7WWWYE7PcGqo5EBQsVRRLF1TFKElyrIhkxCB/cI339oSj7Hj06A3uXm7LL7NWUVJY8aVC0m/b60DVXY75GeTyegVez/5MxNUt4ZW1jlk8VSJZATz3ow43GLpjJ8/F/TD2fbl+ebVj9v5i7ev7g8xnc5nL2+neDaFfUxP9+X65WeyPSRc+egPIxPPbidjKr/VhZCYkJvo5TY0/Qnifqbc3FB/wfA8SaMkSIIwTrNxehNPbxzeW5tJF8OWN5SOFMe5dMvh6TNei5EQsRFqDU/Y/WEju3ZKijnQRhiaxKuUpqTQFyaClMqFCiVDISHTtXSNsdQ47cQG3aQkBpuV4NrKyipRika7tUyOZmJBxxpQYS+hOIBl06Cp4PJlhQEONlJXzE3qHeFyOp09MAXeAZdJQuYZq7LQl4WjNaBFLd+G1QxeSEsI1KBNR5VT1Tz7IrzOTLGDol1ZRxaonZ3cK14R9Amif6MlIYSmJPtN0mKqRAc9cmxGm9m9qTHzUpE7cNdEqC2Raksoy1SF5tYSWls5qZB17tqG3JcaRy7P5AceofdxNqJhe5e6cL/3cQqAf2Q9/pJ11a6g/4F/f9pt331ksM0eBEGcZTT0umCt6sBkT/qgI/PlD24IafB0VjEPKWn6BhllCsSsH7B2l7d1TAhIFoM1lfbrw19wwxI3fv8ViApsEzNQ2O/+/jc6tzDkeN2w/+qNeyNN0K4VDI1eo+rUdWVPY3oMpT8B5BEIs3jca+fZFNzZNcpaApePbNcQCUn1wQ6EHB2FBCCIIl0TUpBh5x8TVglxEgllnrxJ2+FFpb79+OSCIBLjX7ikhKACjhEoLgsiMf6OSxkxOfsFL56W86tjbLqoOqQW4v7m34UMPgRco0IPuwqy/xzv7Y+S0k9HOw12VVlwIg1kIbs7/r/AB7SDJZs=
sidebar_class_name: 'post api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'Ingest external message'}></Heading>

<MethodEndpoint
  method={'post'}
  path={'/api/messaging/ingest-external'}
  context={'endpoint'}
></MethodEndpoint>

Ingest a message from an external source for agent processing

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails parameters={undefined}></ParamsDetails>

<RequestSchema
  title={'Body'}
  body={{
    required: true,
    content: {
      'application/json': {
        schema: {
          type: 'object',
          required: ['channel_id', 'server_id', 'author_id', 'content'],
          properties: {
            channel_id: { type: 'string', format: 'uuid', description: 'ID of the channel' },
            server_id: { type: 'string', format: 'uuid', description: 'ID of the server' },
            author_id: {
              type: 'string',
              format: 'uuid',
              description: 'ID of the original author from the platform',
            },
            author_display_name: { type: 'string', description: 'Display name of the author' },
            content: { type: 'string', description: 'Message content' },
            source_id: { type: 'string', description: 'Original platform message ID' },
            source_type: { type: 'string', description: 'Source platform type' },
            raw_message: { type: 'object', description: 'Raw message data from platform' },
            in_reply_to_message_id: {
              type: 'string',
              format: 'uuid',
              description: 'ID of the message this is replying to',
            },
            metadata: { type: 'object', description: 'Additional metadata' },
          },
        },
      },
    },
  }}
></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '202': {
      description: 'Message ingested and published to bus',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              message: { type: 'string' },
              data: {
                type: 'object',
                properties: {
                  messageId: { type: 'string', format: 'uuid', description: 'Created message ID' },
                },
              },
            },
          },
        },
      },
    },
    '400': {
      description: 'Invalid external message payload',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: { type: 'string', description: 'Error message' },
            },
            title: 'Error',
          },
        },
      },
    },
    '500': {
      description: 'Failed to ingest message',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: { type: 'string', description: 'Error message' },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
