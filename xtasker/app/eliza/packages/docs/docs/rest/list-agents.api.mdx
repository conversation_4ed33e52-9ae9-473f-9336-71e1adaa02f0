---
id: list-agents
title: 'List all agents'
description: 'Returns a list of all available agents'
sidebar_label: 'List all agents'
hide_title: true
hide_table_of_contents: true
api: eJzFVk1v2zgQ/SvEHAPFdnexF92CrndhoN0WSYo9JDmMqZE0DUWq5MiJa/i/L4aSY+cDaG7riylx5s0H3zxqB4JNgvIGsCEvCe4KqCjZyL1w8FDCJckQfTJoHCcxoTbonMENssO1IzP5FRB6iqhOqwpKUOOLw1ak1AefKEG5g98WC/17HuXTAfvgYoMX8qKW2PeObYaef09qvoNkW+pQV7LtCUoI6+9kBQrooyYiPAZLg7WU0onhOgRH6KEAesSudwSlxIH2BVQo70CcMjzaYYy4hQJYqEu/9ufqxCZJZN9AAXWIHQqUMAxcwcsz+Ob5x0CGK/LCNVM0dYhG2qn7sC/AY0dvAT8H+gc70jY/c02CMqS3nMkPXeaGFd6Q1uin5SuafBxiJC9mBHsRY1+AsGirIXNi5esA+8OvgD/eosQyxhBNJIlMG/bN/8ONGl1ScpBm82tMG6p3HMNYWrbdF9BRSti82+1growlQXZvHt1zzz+zIVUml2HYj3zTzfEEDseTQ8B0LB1JG3SYG8qVorRQwhx7nj+dRaK4oagKsoMhOiihFenL+dwFi64NScrfF4vFq4w+6bapaEMu9F1mTkaC/Z3SrA65qENWjn+i+XJlLr6uXiFdfF2ZKthBQXJJeTieXDYfZovZh4U5NxemdvTIWbR8ZZLFUcEuViO1TB2xo4cQ72e3/tZft5w0oOFkKkrceKqMBLMmMySqzANLq6qohbitiYP3ytIxMPsk6C3NzNJXfWAvSXUzPOTkbCQU9k1x6zv02Ogy58ReKOqI+WbEP+SWjLQxDI1GvFxeXY+WNVqacqVDqiE26PknZbCQp7AOGlkxq9Ah+1Te+nNzdna1TULd2VlpxtX5A1dknnQ85ZTIbzgGn48oJ0u6HAFGgVeAvDKOa7JbOzX4aH2COTp+pi7E7dGxy8/ZK4bQvQr0OXOefaMu4wOZFn3lcutsi96TS2MTJ0pOKQ4VhxxIF6aPQWc+y4ma9kS2NWnrpaXET9lVjOrzFzsyQ+8CVmNFuvEquevlUo2v45CEKrN8JDtkHi5PWveyA//S+irYexJ1vSR058IdGRu6bvCTopkNoxmtZqsvUICWNZJ+JPWkhWizFo5XwHFYPo5YopfTcTRTOZ83LO2wntnQzUmtQxr/s1L3IUmH/gQxX8751j9M/bMJ3B0F+f1fC5NeCT3KvHfIXvUsZ7mbhOYGsGcoTj5MVE30/W63xkTfotvv9fWPgeIWypu7AjYYWePo076AlrCimLXpnraqFdZSr1q2QTfky/vl/aHy86R7fy+vYb//D6BBK/g=
sidebar_class_name: 'get api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'List all agents'}></Heading>

<MethodEndpoint method={'get'} path={'/api/agents'} context={'endpoint'}></MethodEndpoint>

Returns a list of all available agents

<ParamsDetails parameters={undefined}></ParamsDetails>

<RequestSchema title={'Body'} body={undefined}></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '200': {
      description: 'List of agents',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              data: {
                type: 'object',
                properties: {
                  agents: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        id: {
                          type: 'string',
                          format: 'uuid',
                          description: 'Unique identifier for the agent',
                        },
                        name: { type: 'string', description: 'Name of the agent' },
                        status: {
                          type: 'string',
                          enum: ['active', 'inactive'],
                          description: 'Current status of the agent',
                        },
                      },
                      title: 'AgentInfo',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    '500': {
      description: 'Error retrieving agents',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
