---
id: process-audio-message
title: 'Process audio message'
description: 'Process an audio message - transcribe and get agent response'
sidebar_label: 'Process audio message'
hide_title: true
hide_table_of_contents: true
api: eJztV0tv2zgQ/iuDuWwbyLG72150y7ZewMAWDRIXe0hyoMWxxJYiVZJy4hr674sh6UedNAlQIKf6Ytma+fjNg/PYYBC1x/IKRS+VxZsCJfnKqS4oa7DEc2cr8h6EgSgBLXkvaoIRBCcMiy4IhJFQUwBRkwngyHfWeMICbUdOMNRMYoldAjtjoI8JBwvshBMtBXLMY4NGtIQlRqiZxAIV8+hEaLBAR9965UhiGVxPBfqqoVZgucGw7ljNB6dMjQUurWtFwBL7Xkkcju2afQC7hNBQ4ozDTQInH/62cs2Ix2dV1gQWLTfY9jqoTrgw5mNGUoTI4R4bu/hCVWAbHXsiKPL8dqk0Pcp5oYxwazwmHR0HrA3BQvYm29Z7cuzhp9zwcy8wAiM5a9tfQ2IEHOKHXZpSIZr952TCXw/ZlG0hCb6v+GnZa80OOHC66DqtqphN4y+elZ/v8Yx6ILiwVpMwWCDdibbjgHCYOVFyNB9HDHQXHnLTj9bNd3dEQtQ48MnT2mecmn/4/YUainRPLx6BeDKJMmi6zltoeKWW4DuiqgEyYqFJvs5R5Di+fSh0M7MSWknI9+aFo7UU2nO4yDnrnsasrHyGx6cMBlF2KDDXuueqbcVjsQlCaf+05ocoSBKiGaBMCh+/TJ4PKrC16QjcRuPtAxcp1l5jAyxtb+TvaLxYNN68ux+Nz8b3XWddIAktSSUgnvw7KC8UlHcPFazEIfcaZepUA38H5WWCEomFxsZJ0MZ+Eae6EseiU+MYi/Emz33DOIdptI2RJ7fazoi901hiE0JXjsfaVkI31ofyr8lkco/pv/waJK1I267lGpmQ4sjHvKOxW7ZafRfw6RLOzmf3O+f5DKStegaJpsLSOtiprN6cTk7fTGAEZ7DUdKcWOk3GvhKaOyqczfKEvOSJ99a6r6fX5trMG+X5QFAeJHlVG54WLCziWCbhVoUGBEQ79RpcbwwnbzpYGR+EqegUpkZ2VpngQWhtbyO5ypEIytTFtWmFETU/Rk7KBHKi4ncJf8vNQ2ic7Ws+8WJ6OU+SS1FR5kpbqtbVwqjvFMFsnP2Wlk9mTGlboYwvr80ITk4u1z5Qe3JSQnoa3SpJsFsOfKREZqWcNTFEkSzxYwKIzc0zQGpzWi2pWlfZwXvpA8yk+JFa69Z7xTb+jlo8qN47KK0lytSskncUaISROrquaoQxpH1yYk7JTJHzNB50ONHGKsOiabTyaxMa8mrHTirBOv/wTN932gqZLIot45jcfDpl4bnrPfeV6R1VfczD6YHrjj3wHy0ubfWVAqtekNCjoFqCyrZtb3Klg5USkKROZ5+wQDYrJX1K6lwjRRVrZF7Rdpn/PmEFHjb3V9OX43GtQtMvTivbjomlrU/fyNWAi0ArzAHibts8XDWP7+FmX65/dT3NNY4n83GnhTJxmWILNrk2XaHoFOaxGwss93vpjwXqpkCuQayx2SyEp89ODwP//a0nt8by6qbAlXCKK0EsYlL5OGfnlvCIla8u8ir6Go731p/YkP8UhkOyErrnX1jgV1of7NbDzVBgQ0KSi4zS2/fp3NE8DSxb7Yd23qHYKp1VFXXhQPxeH+Vyu6v/558u51jgIq/abWxycXWJuMX+sby6GYbhfx/xzCM=
sidebar_class_name: 'post api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'Process audio message'}></Heading>

<MethodEndpoint
  method={'post'}
  path={'/api/audio/{agentId}/process-audio'}
  context={'endpoint'}
></MethodEndpoint>

Process an audio message - transcribe and get agent response

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails
  parameters={[
    {
      name: 'agentId',
      in: 'path',
      required: true,
      schema: { type: 'string', format: 'uuid' },
      description: 'ID of the agent',
    },
  ]}
></ParamsDetails>

<RequestSchema
  title={'Body'}
  body={{
    required: true,
    content: {
      'multipart/form-data': {
        schema: {
          type: 'object',
          properties: {
            file: { type: 'string', format: 'binary', description: 'Audio file to process' },
            userId: { type: 'string', format: 'uuid', description: 'ID of the user' },
            roomId: { type: 'string', format: 'uuid', description: 'ID of the room' },
          },
        },
      },
    },
  }}
></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '200': {
      description: 'Audio processed successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              data: {
                type: 'object',
                properties: {
                  text: { type: 'string', description: 'Transcribed text' },
                  response: { type: 'string', description: "Agent's response" },
                  audioResponse: {
                    type: 'string',
                    format: 'binary',
                    description: "Agent's audio response (if speech enabled)",
                  },
                },
              },
            },
          },
        },
      },
    },
    '400': {
      description: 'Invalid request',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '404': {
      description: 'Agent not found',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '415': {
      description: 'Unsupported media type',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '500': {
      description: 'Error processing audio',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
