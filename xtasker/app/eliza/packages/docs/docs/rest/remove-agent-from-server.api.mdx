---
id: remove-agent-from-server
title: 'Remove agent from server'
description: 'Remove an agent from a server'
sidebar_label: 'Remove agent from server'
hide_title: true
hide_table_of_contents: true
api: eJztVk1vGzcQ/SuDObXG2qu06WVvRqMAAhIksF30EPswIkdaJlxyQ3JlK8L+92LIlSxXbtND4VN00e5y5s2bD87MDhOtIzafsOMYaW3cGu8q1BxVMH0y3mGDV9z5DQM5oDW7BKvgOyCIHDYcsELfcyCRXWhsMGTpS5F8G3x3vZfqKVDHiYOY26GjjrHBArLQWKERWz2lFisM/HUwgTU2KQxcYVQtd4TNDtO2z3opCNcKVz50lLDBYTAax7E6QGey/yPynSjH3rvIUeR/mc3k72msst9QYqAhDkpxjKvB2i1WqLxL7JJoUd9bo3LU6s9RVHenVPzyM6sksQsS42SK4Qn1SHDpvWVyOOZfha+fo7ZwG7JGg0SAY3oJPhXyA3W9ZWxWZCOPFXIIPnwfU3nNzyXlqUtzAYMsO1ZTCf9ntb34KMeJjI3f13yTBVlDdgOMK0UihyXyySTxtpjAfTZen2aj3AvwYbpUzidY+cHpH3l5qbz89twtKRzyBTZuXZLzIyUvk5JMLLVexohmy4nz3EgtNlhTb+rDkKrL3Ij1bj9AxjqnKta7qe+PWE3TpQycIVhssE2pb+raekW29TE1v85msxPu7+QYNG/Y+r6T6zkNOxkC4kl2f8/fmm8EH67h8uPiBOny4wK0V4OAZOdh5QMcVDavLmYXr2ZwDpewsvxgllYGrYaoyJK8XC4OU5c6vvfhy8Wtu3U3rYliEEwEzdGsHWtIHpYMQ2QN9ya1QJD9tFsIg3NSzsWwcTGRU3wBc6d7b1yKQNb6+0xOBaZk3Lq6dR25HO4qczIucSAlZwV/zy1CaoMf1mLxan59UyRXpHjiynuqPqzJmW+cwTyklmHlxbJgat+RcbG5dedwdna9jYm7s7MGytP5vdEMh10jZkrsNiZ4l1OUybI8FoA8iqMAlKFszYrVVk0BfpQ+wiyK77nzYfuo2OX3rBW8704Mvd9XpaiUF4aWnLY5dKol59jGEsSpJCeKgzY+G5IH6IOX7pD7joj2zKqFuHWp5WgO7LQh0XlrLMPQW0+6eCQHJ+Ru5nMRvglDTKxh/sBqyHU4Pwrd3yPwJy+vvfrCSVSvmOx5Mh2D8l03uKn3wcYQFKmLxQesUNwqRV+KeuqapHLXnJayQ+X/XrCS7EWPVzM2db02qR2WF8p3NYu0j+VfljvsfUwduSPE/Xb6uJoeFtMnV3H32MO/u9FOnS3xQ6p7S8ZJ58ssd1M/+oTUG6yO1ubHblNhc7TWlhsiH/cL6V2F0noEZLdbUuQ/gh1H+fx14LDF5tNdhRsKRhpA7l3aRHnW02z4F89+upr225/hn/yYPpKT0G/IDvKGFX7h7fFCLpv0C9rdB2e8GytsmTSH7Ho5vVSK+3SkdzKEpTMfhseb+bv5zRzH8S8xhneK
sidebar_class_name: 'delete api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'Remove agent from server'}></Heading>

<MethodEndpoint
  method={'delete'}
  path={'/api/messaging/servers/{serverId}/agents/{agentId}'}
  context={'endpoint'}
></MethodEndpoint>

Remove an agent from a server

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails
  parameters={[
    { name: 'serverId', in: 'path', required: true, schema: { type: 'string', format: 'uuid' } },
    { name: 'agentId', in: 'path', required: true, schema: { type: 'string', format: 'uuid' } },
  ]}
></ParamsDetails>

<RequestSchema title={'Body'} body={undefined}></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '200': {
      description: 'Agent removed successfully',
      content: {
        'application/json': {
          schema: { type: 'object', properties: { success: { type: 'boolean' } } },
        },
      },
    },
    '400': {
      description: 'Invalid request',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '404': {
      description: 'Server or agent not found',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '500': {
      description: 'Error removing agent',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
