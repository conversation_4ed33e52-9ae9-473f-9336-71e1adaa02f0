import type { SidebarsConfig } from '@docusaurus/plugin-content-docs';

const sidebar: SidebarsConfig = {
  apisidebar: [
    {
      type: 'doc',
      id: 'rest/eliza-os-api',
    },
    {
      type: 'category',
      label: 'system',
      items: [
        {
          type: 'doc',
          id: 'rest/get-ping',
          label: 'Ping health check',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/get-hello',
          label: 'Basic health check',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/get-status',
          label: 'Get system status',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/get-health',
          label: 'Health check endpoint',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/stop-server',
          label: 'Stop the server',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/get-debug-servers',
          label: 'Get server debug info',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/get-servers',
          label: 'Get server debug info',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/get-local-env',
          label: 'Get local environment variables',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/update-local-env',
          label: 'Update local environment variables',
          className: 'api-method post',
        },
      ],
    },
    {
      type: 'category',
      label: 'agents',
      items: [
        {
          type: 'doc',
          id: 'rest/list-agents',
          label: 'List all agents',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/create-agent',
          label: 'Create a new agent',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/get-agent',
          label: 'Get agent details',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/update-agent',
          label: 'Update agent',
          className: 'api-method patch',
        },
        {
          type: 'doc',
          id: 'rest/delete-agent',
          label: 'Delete an agent',
          className: 'api-method delete',
        },
        {
          type: 'doc',
          id: 'rest/start-agent',
          label: 'Start an agent',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/stop-agent',
          label: 'Stop an agent',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/get-agent-panels',
          label: 'Get agent panels',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/get-all-worlds',
          label: 'Get all worlds',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/create-agent-world',
          label: 'Create a world for an agent',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/update-agent-world',
          label: 'Update a world',
          className: 'api-method patch',
        },
        {
          type: 'doc',
          id: 'rest/get-agent-rooms',
          label: 'Get agent rooms',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/create-room',
          label: 'Create a room',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/get-room',
          label: 'Get room details',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/update-room',
          label: 'Update a room',
          className: 'api-method patch',
        },
        {
          type: 'doc',
          id: 'rest/delete-room',
          label: 'Delete a room',
          className: 'api-method delete',
        },
        {
          type: 'doc',
          id: 'rest/get-room-memories',
          label: 'Get room memories',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/send-message',
          label: 'Send a message to an agent',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/send-audio-message',
          label: 'Send an audio message',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/synthesize-speech',
          label: 'Convert text to speech',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/generate-speech',
          label: 'Generate speech from text',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/conversation-to-speech',
          label: 'Process conversation and return speech',
          className: 'api-method post',
        },
      ],
    },
    {
      type: 'category',
      label: 'memory',
      items: [
        {
          type: 'doc',
          id: 'rest/get-room-memories',
          label: 'Get room memories',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/delete-room-memories',
          label: 'Delete all memories for a room',
          className: 'api-method delete',
        },
        {
          type: 'doc',
          id: 'rest/get-agent-memories',
          label: 'Get agent memories',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/delete-all-agent-memories',
          label: 'Delete all agent memories',
          className: 'api-method delete',
        },
        {
          type: 'doc',
          id: 'rest/update-agent-memory',
          label: 'Update a memory',
          className: 'api-method patch',
        },
        {
          type: 'doc',
          id: 'rest/create-memory-room',
          label: 'Create a room',
          className: 'api-method post',
        },
      ],
    },
    {
      type: 'category',
      label: 'rooms',
      items: [
        {
          type: 'doc',
          id: 'rest/get-agent-rooms',
          label: 'Get agent rooms',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/create-room',
          label: 'Create a room',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/get-room',
          label: 'Get room details',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/update-room',
          label: 'Update a room',
          className: 'api-method patch',
        },
        {
          type: 'doc',
          id: 'rest/delete-room',
          label: 'Delete a room',
          className: 'api-method delete',
        },
        {
          type: 'doc',
          id: 'rest/get-room-memories',
          label: 'Get room memories',
          className: 'api-method get',
        },
      ],
    },
    {
      type: 'category',
      label: 'messaging',
      items: [
        {
          type: 'doc',
          id: 'rest/submit-message',
          label: 'Submit a message',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/complete-message',
          label: 'Complete a message',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/ingest-external-message',
          label: 'Ingest external message',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/get-central-servers',
          label: 'Get central servers',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/create-server',
          label: 'Create server',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/add-agent-to-server',
          label: 'Add agent to server',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/get-server-agents',
          label: 'Get server agents',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/remove-agent-from-server',
          label: 'Remove agent from server',
          className: 'api-method delete',
        },
        {
          type: 'doc',
          id: 'rest/get-server-channels',
          label: 'Get server channels',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/create-channel',
          label: 'Create channel',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/get-dm-channel',
          label: 'Get or create DM channel',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/create-central-channel',
          label: 'Create central channel',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/get-central-server-channels',
          label: 'Get central server channels',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/get-channel-details',
          label: 'Get channel details',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/get-channel-info',
          label: 'Get channel info',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/update-channel',
          label: 'Update channel',
          className: 'api-method patch',
        },
        {
          type: 'doc',
          id: 'rest/delete-channel',
          label: 'Delete channel',
          className: 'api-method delete',
        },
        {
          type: 'doc',
          id: 'rest/get-channel-participants',
          label: 'Get channel participants',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/add-agent-to-channel',
          label: 'Add agent to channel',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/get-channel-messages',
          label: 'Get channel messages',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/send-channel-message',
          label: 'Send message to channel',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/delete-all-channel-messages',
          label: 'Delete all channel messages',
          className: 'api-method delete',
        },
        {
          type: 'doc',
          id: 'rest/delete-channel-message',
          label: 'Delete channel message',
          className: 'api-method delete',
        },
        {
          type: 'doc',
          id: 'rest/delete-all-user-channel-messages',
          label: 'Delete all channel messages by user',
          className: 'api-method delete',
        },
        {
          type: 'doc',
          id: 'rest/create-group-channel',
          label: 'Create group channel',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/process-external-message',
          label: 'Process external message',
          className: 'api-method post',
        },
      ],
    },
    {
      type: 'category',
      label: 'audio',
      items: [
        {
          type: 'doc',
          id: 'rest/send-audio-message',
          label: 'Send an audio message',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/synthesize-speech',
          label: 'Convert text to speech',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/generate-speech',
          label: 'Generate speech from text',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/conversation-to-speech',
          label: 'Process conversation and return speech',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/transcribe-audio',
          label: 'Transcribe audio',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/process-audio-message',
          label: 'Process audio message',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/synthesize-speech',
          label: 'Synthesize speech from text',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/generate-speech',
          label: 'Generate speech from text',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/conversation-to-speech',
          label: 'Convert conversation to speech',
          className: 'api-method post',
        },
      ],
    },
    {
      type: 'category',
      label: 'media',
      items: [
        {
          type: 'doc',
          id: 'rest/upload-agent-media',
          label: 'Upload media for agent',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/upload-channel-media',
          label: 'Upload media to channel',
          className: 'api-method post',
        },
      ],
    },
    {
      type: 'category',
      label: 'logs',
      items: [
        {
          type: 'doc',
          id: 'rest/get-logs',
          label: 'Get system logs',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/post-logs',
          label: 'Get system logs (POST)',
          className: 'api-method post',
        },
        {
          type: 'doc',
          id: 'rest/clear-logs',
          label: 'Clear system logs',
          className: 'api-method delete',
        },
        {
          type: 'doc',
          id: 'rest/get-agent-logs',
          label: 'Get agent logs',
          className: 'api-method get',
        },
        {
          type: 'doc',
          id: 'rest/delete-agent-log',
          label: 'Delete a specific log entry',
          className: 'api-method delete',
        },
      ],
    },
    {
      type: 'category',
      label: 'websocket',
      items: [
        {
          type: 'doc',
          id: 'rest/web-socket-connection',
          label: 'WebSocket Connection',
          className: 'api-method get',
        },
      ],
    },
    {
      type: 'category',
      label: 'messages',
      items: [
        {
          type: 'doc',
          id: 'rest/send-message',
          label: 'Send a message to an agent',
          className: 'api-method post',
        },
      ],
    },
  ],
};

export default sidebar.apisidebar;
