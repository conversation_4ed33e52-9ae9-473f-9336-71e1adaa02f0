---
id: start-agent
title: 'Start an agent'
description: 'Starts an existing agent'
sidebar_label: 'Start an agent'
hide_title: true
hide_table_of_contents: true
api: eJztVsFu4zYQ/RViTm2g2N52e9HNaFzAQBcJ4hQ9JD6MybHEDUVqScqJ19C/F0PKjhMn25xyWl8siTOPb4bDN7ODiFWA8hawIhsDLAtQFKTXbdTOQgmLiD4GgVbQow5R20okUyjAteSRzeYKSghsOB2WWvTYUCTP2Duw2BCUeY+5ggI0Q7cYayjA07dOe1JQRt9RAUHW1CCUO4jblhKy17aCAtbONxihhK7TCvqXVOcXwq1FrCkzFNGJRAr6Je8SWmcDBQb+bTLhv+fuiXv2ICVCJyWFsO6M2UIB0tnIoZU7wLY1WqbAx18Du+5OObvVV5IpE57TFHXeeEA9Mlw5ZwgtFECP2LSGcho4OIzvQNTqXZnKJ/DSsC/42GIXXsMg2zWpMmTUG4JlP/wK+Pxa+uZ2g0arIffziw9O2hpN4KyR987/P6Z06pV8vKyoGYOJZNsX0FAIWL3bbW+e6jSiNq9m+bnnRTIkJVIYQtt8kLyYUx915GjzFrA/js9vVbN1UaxdZ9XP0/io0/jjtcuROSRtOVbQn0fyAUeSiMXacZNqXcjtKdZQwhhbPc6Nb7wbmlM/zj2jgEB+s29gnTdQQh1jW47Hxkk0tQux/H0ymZxQ/JuXhaINGdc2qackpNSGmHCKck/T6O8oLhdiejU/QZpezYVysmOQFKNYOy8OLptPo8no00Sci6lYG3rUK0MCrRJBokF+mc4HOV5zO35w/n50Z+/sTa0Dbyh0EIqCriwp7pYrEl0gJR50rAWKFKfZCt9Zy1WbN9Y2RLSSRmJmVeu05enAGPeQyElPyCVe3NkGLVb8mDhpG8lzK7FVxt9zCyLW3nUV73g9W9xkyzVKGrjSnqrzFVr9nRKYS41+7XhnxlSuQW1DeWfPxdnZYhsiNWdnpchP5w9akTgMLCFRIrvR3tl0RIks8WMGSPIZGCALqdFrkls5JPjJ+ggzO36hxvntk2OT3pOXd6452ehLugTaVuySX0jUaJVJqZM1Wksm5CQOJTlQ7JR2aSN+EK13LAJJXti0JZK1CFsbawr6wE5pZJ+/tCHRtcahyhHxwgm5m9mMjW98F3gmmj2S7FIdzo5S9zID/9Jq4eQ9RXa9JjTnUTckpGuazg4SJzYaRbYazS+hAA4rF30u6kEcUSZxHObHQ+X/mbEiT2ZPVzOU43GlY92tRtI1Y2JrF/I/sAzw7W/QHiGm6ZaH270iP7uAuyeB/tEcPChWpMc4bg1qy4qWaO0GpbkFbDUU+yG7gPJpFM5ysyyAFYVNd7sVBvrHm77nz9868lsob5cFbNBrvtdJkpQO/KwGZf8B9V+uhwn7V/HmiPxGFMNHtJzpDZqO36CAe9oezfP9si+gJlTkE7W8OpWS2njkd9LiWBAP0nx1ubiBvv8PjnRbjA==
sidebar_class_name: 'post api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'Start an agent'}></Heading>

<MethodEndpoint
  method={'post'}
  path={'/api/agents/{agentId}/start'}
  context={'endpoint'}
></MethodEndpoint>

Starts an existing agent

<Heading
  id={'request'}
  as={'h2'}
  className={'openapi-tabs__heading'}
  children={'Request'}
></Heading>

<ParamsDetails
  parameters={[
    {
      name: 'agentId',
      in: 'path',
      required: true,
      schema: { type: 'string', format: 'uuid' },
      description: 'ID of the agent to start',
    },
  ]}
></ParamsDetails>

<RequestSchema title={'Body'} body={undefined}></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{
    '200': {
      description: 'Agent started successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: true },
              data: {
                type: 'object',
                properties: {
                  id: { type: 'string', format: 'uuid' },
                  name: { type: 'string' },
                  status: { type: 'string', enum: ['active'] },
                },
              },
            },
          },
        },
      },
    },
    '400': {
      description: 'Invalid agent ID',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '404': {
      description: 'Agent not found',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
    '500': {
      description: 'Error starting agent',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: { type: 'boolean', example: false },
              error: {
                type: 'object',
                properties: {
                  code: { type: 'string', description: 'Error code' },
                  message: { type: 'string', description: 'Error message' },
                  details: { type: 'string', description: 'Detailed error information' },
                },
              },
            },
            title: 'Error',
          },
        },
      },
    },
  }}
></StatusCodes>
