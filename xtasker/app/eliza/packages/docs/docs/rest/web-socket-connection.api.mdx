---
id: web-socket-connection
title: 'WebSocket Connection'
description: 'WebSocket connection for real-time communication.'
sidebar_label: 'WebSocket Connection'
hide_title: true
hide_table_of_contents: true
api: eJztWEtv2zgQ/isD9mbIdoK96ZYN3EUW6QNxih7qAKbJscSWIlVyZNc1/N8XQ8mxY1vFLhZY7CGXmOIMZ755D7IVJIso8i9ijYvo1Tck8ZQJjVEFU5PxTuTiMy6miQTKO4eKr2HpAwSUdkimQlC+qhpnlGTaCGZu5gaD2wP3p4f7wSCH+Trm47H1StrSR8p/u7q6GrdqR8aP5+27yQodxcEg5883b97ArTXoCMjDFMMKA7Qc+cwNYf7VGzeHIfzpjQMJwftqrErpHNqZA5jP51+jd3zc8h+AmWCeOz0TOcxE0xg9E9meJAt09JLGpF0nihENYW5RrpCV3vPhv9JaYYyySHqn6DRI6G76NBL+oFZmpGBccaTxF2CaiKGH5GSFLwVegBnwe4ORhmsfrB5GkpQgP7TXoJoQOJqJ8Pd9daqI86LLBvL7DDnOi843vwcvtZKRGMN7XO99Bos9oQ9DRKd7PdES35/5I/t3vo/Joh6iCigJ9U0nlwsvkqzq4/e+CaoPkrkslkrfFGUfWJkKODL1y4H8dKATSVVW7PfE89SfuLe+qi222fCui0IdvMIYjSu4hyRyXzy68voHnrsA5SQpP/Nnm4rQ1PoXGZlKNJm43b0oldO7DubpdeeD7vpST/HFxFHYtLWyb6zWF4B83YfL4gpta7RrqgWG48iafXpeypUqFn0xP29HL2nneX/BIAzBB7ZmwgdArs0+KxJvDxqNJI296DiRiYCx9i5iFPlWXF9d88/L8TVdG1IlZ9jH4MkrbyNH/tJQYx8trIklarHbZaJCKr0WuSiQRCZqSaXIxfgwK7Mu73iEbkUTrMhFSVSfTTlxOlTvmQyao+frKjXEJEnsnjJh3NKzIWTIosjFxJqfEj5M4ebj3Zmkm493oL1qWIh8ns3PT1bXo6vR9RUM4QaWFn+YhUWQTkNU0kr+uLmDFFVYBlnh2odvIw7hY2kiKwQTQWM0hUPNvXaB0ETUsDZUgoRkp91AaJxjJ7eKjYskncIRTJyuvXEUQVrr1wlc6mTGFdnMVdLJgo8Jk3GEgVuOK1r5e2wRqAzcp0DCw2T62HIupcIOK+6h+lBIZ35iEuaBSoSlZ80sU/tKGtdOiMFguomEFS8m7Wm4NhrB1xiSH2OChG5lgncpRAksVimRWcBN0W0qkE5gzRLVRnUOPnAfyWwfvsPKh83hYZW+0yueDmeK2nZpXMFP9r2zlE7b5Lp9z2md2KVkB7HRxidFfDjut4m1RlQlxI2jEqN5RqeN5DdvjeW+aL3UrUVMOAP3OJkw82NoIqGGyQ9UTcrDyZHrTj3wXH/89OHyIgkrI6HlGt19EJlgs9qkb5NaZEJ5R1IRVwtvJ8fFctvKoo3IjkqTN9DCUNksRspXY2RuH9vfVPS1j1RJdyTx0CsOK+1pGW4TEnT0ujC/LsyvC/Prwvy6ML8uzP/HhZk2Nc80LrdxbaVxYteNx2234J78M4gHD19utwsZ8VOwux1ff28wbET+5SkTKxkMb5L8dbw1/zF5FLvdX1tQD50=
sidebar_class_name: 'get api-method'
info_path: docs/rest/eliza-os-api
custom_edit_url: null
---

import MethodEndpoint from '@theme/ApiExplorer/MethodEndpoint';
import ParamsDetails from '@theme/ParamsDetails';
import RequestSchema from '@theme/RequestSchema';
import StatusCodes from '@theme/StatusCodes';
import OperationTabs from '@theme/OperationTabs';
import TabItem from '@theme/TabItem';
import Heading from '@theme/Heading';

<Heading as={'h1'} className={'openapi__heading'} children={'WebSocket Connection'}></Heading>

<MethodEndpoint method={'get'} path={'/websocket'} context={'endpoint'}></MethodEndpoint>

WebSocket connection for real-time communication.

**Connection URL**: `ws://localhost:3000/socket.io/`

**Events**:

### Client to Server Events:

- `join` - Join a room/channel

  ```json
  \{
    "roomId": "uuid",
    "agentId": "uuid"
  \}
  ```

- `leave` - Leave a room/channel

  ```json
  \{
    "roomId": "uuid",
    "agentId": "uuid"
  \}
  ```

- `message` - Send a message

  ```json
  \{
    "text": "string",
    "roomId": "uuid",
    "userId": "uuid",
    "name": "string"
  \}
  ```

- `request-world-state` - Request current state
  ```json
  \{
    "roomId": "uuid"
  \}
  ```

### Server to Client Events:

- `messageBroadcast` - New message broadcast

  ```json
  \{
    "senderId": "uuid",
    "senderName": "string",
    "text": "string",
    "roomId": "uuid",
    "serverId": "uuid",
    "createdAt": "timestamp",
    "source": "string",
    "id": "uuid",
    "thought": "string",
    "actions": ["string"],
    "attachments": []
  \}
  ```

- `messageComplete` - Message processing complete

  ```json
  \{
    "channelId": "uuid",
    "serverId": "uuid"
  \}
  ```

- `world-state` - World state update

  ```json
  \{
    "agents": \{\},
    "users": \{\},
    "channels": \{\},
    "messages": \{\}
  \}
  ```

- `logEntry` - Real-time log entry

  ```json
  \{
    "level": "number",
    "time": "timestamp",
    "msg": "string",
    "agentId": "uuid",
    "agentName": "string"
  \}
  ```

- `error` - Error event
  ```json
  \{
    "error": "string",
    "details": \{\}
  \}
  ```

<ParamsDetails parameters={undefined}></ParamsDetails>

<RequestSchema title={'Body'} body={undefined}></RequestSchema>

<StatusCodes
  id={undefined}
  label={undefined}
  responses={{ '101': { description: 'Switching Protocols - WebSocket connection established' } }}
></StatusCodes>
