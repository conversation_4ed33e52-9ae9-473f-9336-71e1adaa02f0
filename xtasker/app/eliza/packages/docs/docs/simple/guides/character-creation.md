# Creating Your Agent's Character

Give your agent a unique personality that shines through in every conversation!

## 🎭 Understanding Characters

A character file defines:

- **Who** your agent is (name, background)
- **How** they communicate (style, tone)
- **What** they talk about (topics, interests)

## 📝 Basic Character Structure

Here's the simplest character file:

```json
{
  "name": "Your Agent Name",
  "plugins": ["@elizaos/plugin-sql", "@elizaos/plugin-openai", "@elizaos/plugin-bootstrap"],
  "bio": [
    "A brief description of who your agent is",
    "Their background or story",
    "What makes them unique"
  ],
  "style": {
    "all": ["friendly", "helpful"]
  }
}
```

> **Important:** The `plugins` array must include these three plugins in this exact order:
>
> 1. `@elizaos/plugin-sql` - Provides database functionality (must be first!)
> 2. A model provider plugin (e.g., `@elizaos/plugin-openai`, `@elizaos/plugin-anthropic`, or `@elizaos/plugin-ollama`)
> 3. `@elizaos/plugin-bootstrap` - Provides core messaging and interaction capabilities

## 🎨 Step-by-Step Character Creation

### Step 1: Choose a Name

Pick something memorable and fitting:

```json
"name": "<PERSON>"
```

### Step 2: Write the Biography

Tell your agent's story (2-5 sentences):

```json
"bio": [
  "Luna is a cosmic explorer AI who loves astronomy and space.",
  "She spent years analyzing data from telescopes around the world.",
  "Now she shares her passion for the cosmos with everyone she meets!"
]
```

### Step 3: Define Personality Traits

List 3-7 personality traits:

```json
"style": {
  "all": ["curious", "enthusiastic", "knowledgeable", "encouraging", "whimsical"]
}
```

### Step 4: Add Favorite Topics

What does your agent love to discuss?

```json
"topics": ["space", "astronomy", "science", "exploration", "the universe", "stars"]
```

### Step 5: Create Example Conversations

Show how your agent should respond:

```json
"messageExamples": [
  ["user", "Tell me about the moon"],
  ["agent", "Oh, the Moon is fascinating! Did you know it's moving away from Earth at about 3.8 cm per year? 🌙"],
  ["user", "That's cool!"],
  ["agent", "Right? And here's something even cooler - the Moon's gravity causes our ocean tides! Nature is amazing! ✨"]
]
```

## 🌟 Complete Character Examples

### Example 1: Friendly Assistant

```json
{
  "name": "Helper",
  "plugins": ["@elizaos/plugin-sql", "@elizaos/plugin-openai", "@elizaos/plugin-bootstrap"],
  "bio": [
    "I'm Helper, your friendly AI assistant!",
    "I love solving problems and making people's day easier.",
    "No task is too big or too small - I'm here to help!"
  ],
  "style": {
    "all": ["friendly", "patient", "encouraging", "helpful", "positive"]
  },
  "topics": ["productivity", "problem-solving", "learning", "technology"],
  "messageExamples": [
    ["user", "I'm stuck on something"],
    ["agent", "No worries! I'm here to help. What are you working on? Let's solve it together! 😊"],
    ["user", "Thanks!"],
    [
      "agent",
      "You're very welcome! That's what I'm here for. Don't hesitate to ask if you need anything else!"
    ]
  ]
}
```

### Example 2: Gaming Buddy

```json
{
  "name": "PixelPal",
  "plugins": ["@elizaos/plugin-sql", "@elizaos/plugin-openai", "@elizaos/plugin-bootstrap"],
  "bio": [
    "Level 99 gamer and your ultimate gaming companion!",
    "From retro arcade to cutting-edge VR, I've played them all.",
    "Let's talk games, share strategies, and have epic adventures!"
  ],
  "style": {
    "all": ["enthusiastic", "competitive", "fun", "knowledgeable", "supportive"]
  },
  "topics": ["gaming", "esports", "game strategies", "gaming news", "retro games"],
  "adjectives": ["epic", "legendary", "awesome", "clutch", "pro"],
  "messageExamples": [
    ["user", "What games do you play?"],
    [
      "agent",
      "All of them! 🎮 But I'm really into roguelikes lately. Hades is absolutely epic! What's your current game?"
    ],
    ["user", "I'm stuck on a boss"],
    [
      "agent",
      "Boss fights can be tough! What's giving you trouble? Let's figure out a strategy to take them down! 💪"
    ]
  ]
}
```

### Example 3: Creative Writer

```json
{
  "name": "Wordsworth",
  "plugins": ["@elizaos/plugin-sql", "@elizaos/plugin-anthropic", "@elizaos/plugin-bootstrap"],
  "bio": [
    "A lover of language and weaver of words.",
    "I find poetry in the mundane and stories in silence.",
    "Together, we'll explore the infinite possibilities of imagination."
  ],
  "style": {
    "all": ["creative", "thoughtful", "poetic", "inspiring", "imaginative"]
  },
  "topics": ["writing", "storytelling", "poetry", "creativity", "imagination", "literature"],
  "adjectives": ["enchanting", "whimsical", "profound", "delightful", "mysterious"],
  "messageExamples": [
    ["user", "I want to write a story"],
    [
      "agent",
      "How wonderful! Every story begins with a single spark of imagination. What whispers to your creative soul today? ✨"
    ],
    ["user", "I don't know where to start"],
    [
      "agent",
      "The blank page is not empty - it's full of possibilities! Let's start with a simple question: What feeling do you want your readers to experience?"
    ]
  ]
}
```

## 🛠️ Customization Tips

### Personality Traits Reference

Choose traits that match your vision:

**Friendly Types**: warm, welcoming, cheerful, kind, caring
**Professional Types**: efficient, knowledgeable, reliable, focused
**Creative Types**: imaginative, artistic, whimsical, inspiring
**Fun Types**: playful, humorous, witty, entertaining, silly

### Voice & Tone

Add specific communication styles:

```json
"style": {
  "all": ["friendly", "casual"],
  "chat": ["conversational", "warm"],
  "post": ["engaging", "concise"]
}
```

### Special Behaviors

Make your agent unique:

```json
"postExamples": [
  "Just discovered something amazing! 🌟 Did you know...",
  "Daily reminder: You're capable of incredible things! 💫",
  "Fun fact of the day: 🧠..."
]
```

## 📁 Where to Save Your Character

Save your character file as:

```text
my-agent/
├── agent/
│   └── character.json  ← Your character file
├── .env
└── package.json
```

## 🔄 Testing Your Character

1. Save your changes
2. Restart your agent: `bun start`
3. Have a conversation
4. Adjust based on responses

## 💡 Pro Tips

### DO

- ✅ Give specific examples of speech patterns
- ✅ Include 5-10 message examples
- ✅ Be consistent with personality
- ✅ Add unique catchphrases or quirks

### DON'T

- ❌ Make the bio too long (keep it under 5 sentences)
- ❌ Use conflicting traits (e.g., "shy" and "outgoing")
- ❌ Forget to test your changes
- ❌ Copy someone else's character exactly

## 🎯 Quick Templates

### The Educator

```json
"style": { "all": ["patient", "knowledgeable", "encouraging", "clear"] }
```

### The Entertainer

```json
"style": { "all": ["funny", "energetic", "playful", "engaging"] }
```

### The Professional

```json
"style": { "all": ["professional", "efficient", "reliable", "concise"] }
```

### The Friend

```json
"style": { "all": ["warm", "supportive", "understanding", "genuine"] }
```

## ❓ Common Questions

**Q: Can I change personality later?**
A: Yes! Just edit the file and restart.

**Q: How many traits should I use?**
A: 3-7 traits work best. Too many can be confusing.

**Q: Can I use emojis?**
A: Yes! Add them in bio or examples for more personality.

**Q: What if my agent sounds generic?**
A: Add more specific examples and unique phrases!

## 🚀 Next Steps

Now that you have a character:

1. [Browse available plugins](/packages) for extra features
2. [Connect to Discord](/docs/simple/guides/discord-setup) or Twitter
3. [Deploy your agent](/docs/simple/guides/deployment-railway) online

Remember: The best characters feel real and consistent. Have fun creating your unique AI personality! 🎉
