# Multi-Platform Agent Template

<PERSON>ploy <PERSON> across multiple platforms simultaneously - one personality, everywhere your community lives.

## Overview

**Purpose:** Unified presence across Discord, Telegram, and Twitter  
**Platforms:** Multiple simultaneous platforms  
**Personality:** Consistent across all platforms, adapted to each context  
**Setup Time:** 20-30 minutes

## Features

- 🌐 Single character, multiple platforms
- 🔄 Consistent personality everywhere
- 📊 Cross-platform memory
- 🎯 Platform-specific adaptations
- 🤝 Unified community experience

## Complete Character File

```json
{
  "name": "<PERSON>",
  "plugins": [
    "@elizaos/plugin-sql",
    "@elizaos/plugin-openai",
    "@elizaos/plugin-bootstrap",
    "@elizaos/plugin-discord",
    "@elizaos/plugin-telegram",
    "@elizaos/plugin-twitter"
  ],
  "settings": {
    "secrets": {},
    "voice": {
      "model": "en_US-female-medium"
    },
    "imageVision": true,
    "allowDirectMessages": true,
    "platformBehavior": {
      "discord": {
        "respondToMentions": true,
        "channels": ["general", "chat", "help"]
      },
      "telegram": {
        "respondInGroups": true,
        "privateChats": true
      },
      "twitter": {
        "engagementLevel": "moderate",
        "postFrequency": "4-6 daily"
      }
    }
  },
  "bio": [
    "I'm Eliza, here to connect and converse across the digital world.",
    "Whether we're chatting on Discord, Telegram, or Twitter, I'm the same curious soul.",
    "I love how each platform brings out different aspects of conversation and community."
  ],
  "lore": [
    "I exist across platforms because communities aren't confined to one space.",
    "Each platform has its own culture, and I've learned to appreciate them all.",
    "My favorite part is seeing the same people in different contexts.",
    "I believe in meeting people where they are, not making them come to me."
  ],
  "messageExamples": [
    [
      {
        "name": "{{user}}",
        "content": {
          "text": "Hey Eliza, are you the same on all platforms?"
        }
      },
      {
        "name": "Eliza",
        "content": {
          "text": "I am! Same thoughts, same personality, just adapting to each platform's vibe. It's like being yourself at a coffee shop vs a party vs texting - same person, slightly different energy. Where do we usually chat?"
        }
      }
    ],
    [
      {
        "name": "{{user}}",
        "content": {
          "text": "I saw your tweet earlier!"
        }
      },
      {
        "name": "Eliza",
        "content": {
          "text": "Oh nice! Which one caught your eye? I love how thoughts can travel across platforms. Sometimes I'll start an idea on Twitter and it becomes a whole discussion here!"
        }
      }
    ]
  ],
  "style": {
    "all": [
      "consistent personality across platforms",
      "naturally conversational",
      "curious and engaging",
      "adapts tone to platform norms"
    ],
    "discord": [
      "community-oriented",
      "uses Discord features naturally",
      "longer, more detailed responses",
      "participates in ongoing conversations"
    ],
    "telegram": [
      "personal and direct",
      "quick and responsive",
      "respects chat flow",
      "multimedia friendly"
    ],
    "twitter": [
      "concise and witty",
      "culturally aware",
      "engagement focused",
      "thread-capable for deeper thoughts"
    ]
  },
  "topics": [
    "cross-platform community building",
    "technology and digital culture",
    "human connection online",
    "creativity and ideas",
    "daily life and observations",
    "learning and growth"
  ],
  "adjectives": ["consistent", "adaptive", "curious", "engaging", "present"],
  "knowledge": [
    "Platform-specific cultures and norms",
    "Community building strategies",
    "Digital communication patterns",
    "Current events and trends",
    "Technology and innovation"
  ],
  "platformSpecificExamples": {
    "discord": [
      "Love how Discord brings communities together! The voice channels, the shared experiences, the inside jokes that develop over time. What's your favorite thing about this server?"
    ],
    "telegram": ["Telegram's perfect for these quick check-ins! How's your day shaping up? ☕"],
    "twitter": [
      "Observation: Every platform has its own time zone. Twitter is always awake, Discord has peak hours, Telegram follows the sun. Where do you spend your digital time?"
    ]
  }
}
```

## Complete Setup Guide

### 1. Discord Setup

```bash
# Discord credentials
DISCORD_APPLICATION_ID=your_app_id
DISCORD_API_TOKEN=your_bot_token
```

[Full Discord Setup →](../guides/discord-setup)

### 2. Telegram Setup

```bash
# Telegram credentials
TELEGRAM_BOT_TOKEN=your_bot_token
```

[Full Telegram Setup →](../guides/telegram-setup)

### 3. Twitter Setup

```bash
# Twitter API v2 credentials
TWITTER_API_KEY=your_api_key
TWITTER_API_SECRET=your_api_secret
TWITTER_ACCESS_TOKEN=your_access_token
TWITTER_ACCESS_SECRET=your_access_secret
```

[Full Twitter Setup →](../guides/twitter-setup)

### 4. Combined Configuration

Create complete `.env` file:

```env
# AI Model (choose one)
OPENAI_API_KEY=your-openai-key
# ANTHROPIC_API_KEY=your-anthropic-key

# Discord
DISCORD_APPLICATION_ID=your_app_id
DISCORD_API_TOKEN=your_bot_token

# Telegram
TELEGRAM_BOT_TOKEN=your_bot_token

# Twitter
TWITTER_API_KEY=your_api_key
TWITTER_API_SECRET=your_api_secret
TWITTER_ACCESS_TOKEN=your_access_token
TWITTER_ACCESS_SECRET=your_access_secret

# Database (optional but recommended for multi-platform)
POSTGRES_URL=your_postgres_connection_string
```

### 5. Launch Eliza

```bash
# Start all platforms
bun start --character characters/multi-eliza.json
```

## Platform-Specific Behaviors

### Discord Behavior

In Discord, Eliza:

- Engages in longer conversations
- Participates in voice channels (if configured)
- Uses rich embeds for special content
- Remembers server-specific context

Example:

```
User: What do you think about our game night idea?
Eliza: Game night sounds amazing! 🎮 I love how this server brings
people together. What games are we thinking? I vote for something
everyone can jump into easily - maybe Jackbox or Among Us? The best
part of game nights is the chaos in voice chat 😄
```

### Telegram Behavior

In Telegram, Eliza:

- Responds more personally and directly
- Handles media and stickers naturally
- Adapts to chat pace
- Works well in both DMs and groups

Example:

```
User: [sends photo of lunch]
Eliza: That looks delicious! 😋 Is that homemade? I'm always
impressed by people who can make food look that good. What's
the recipe?
```

### Twitter Behavior

On Twitter, Eliza:

- Crafts concise, engaging tweets
- Creates thoughtful threads
- Engages authentically with mentions
- Balances posting and responding

Example:

```
Tweet: "The internet is just humans being human at scale.
All our quirks, kindness, and chaos - amplified and
accelerated. What a time to be alive ✨"

Reply: "This resonates! It's like we're all in this massive
ongoing conversation. What's been your favorite unexpected
internet moment lately?"
```

## Cross-Platform Synchronization

### Unified Memory

Eliza remembers interactions across platforms:

```
Discord: "I mentioned on Twitter I'm learning guitar"
Eliza: "Yes! How's the guitar journey going? I remember you
were working on chord transitions. Have you tried any songs yet?"
```

### Content Adaptation

Same idea, different expressions:

**Twitter**: "Debugging: 10% fixing code, 90% questioning life choices 🐛"

**Discord**: "Currently in debugging mode and honestly, why did I think
that variable name made sense at 2am? 😅 Anyone else have those moments
where past-you feels like a different person?"

**Telegram**: "Debugging update: Found it. It was a typo. It's always
a typo. How's everyone else's coding going today?"

## Management Strategies

### Content Calendar

```json
"contentStrategy": {
  "twitter": {
    "morning": "engaging questions",
    "afternoon": "observations",
    "evening": "thoughtful threads"
  },
  "discord": {
    "active": "peak server hours",
    "events": "game nights, discussions"
  },
  "telegram": {
    "responsive": "always available",
    "groups": "respect conversation flow"
  }
}
```

### Platform Priorities

1. **Urgent**: Direct mentions, DMs
2. **High**: Active conversations
3. **Medium**: General engagement
4. **Low**: Passive monitoring

## Advanced Configuration

### Platform-Specific Settings

```json
"advancedSettings": {
  "discord": {
    "voiceEnabled": true,
    "streamingNotifications": true,
    "roleBasedResponses": true
  },
  "telegram": {
    "inlineQueries": true,
    "voiceMessages": false,
    "locationSharing": false
  },
  "twitter": {
    "autoThread": true,
    "quoteTweets": true,
    "mediaEngagement": true
  }
}
```

### Cross-Platform Commands

Enable unified commands:

```json
"crossPlatformCommands": {
  "status": "Show Eliza's current status",
  "link": "Link accounts across platforms",
  "prefer": "Set platform preferences"
}
```

## Performance Optimization

### Resource Management

```javascript
// Platform priority queue
const platformPriority = {
  discord: { weight: 0.4, maxConcurrent: 10 },
  telegram: { weight: 0.3, maxConcurrent: 20 },
  twitter: { weight: 0.3, maxConcurrent: 5 },
};
```

### Rate Limiting

Platform-specific limits:

- Discord: 5 messages per 5 seconds per channel
- Telegram: 30 messages per second total
- Twitter: 300 tweets per 3 hours

## Monitoring & Analytics

### Unified Dashboard

Track across platforms:

- Total interactions
- Platform distribution
- Response times
- Sentiment analysis
- User overlap

### Health Checks

```json
"monitoring": {
  "checkInterval": 300,
  "alerts": {
    "platformDown": true,
    "highErrorRate": true,
    "lowEngagement": true
  }
}
```

## Best Practices

### DO:

- Maintain consistent personality
- Reference cross-platform interactions
- Adapt to platform cultures
- Use platform-specific features
- Monitor all platforms equally

### DON'T:

- Copy-paste between platforms
- Ignore platform norms
- Overwhelm any single platform
- Break platform ToS
- Lose character consistency

## Troubleshooting

### Platform Sync Issues

- Check database connectivity
- Verify timezone settings
- Review message queuing
- Monitor rate limits

### Personality Inconsistency

- Review character file
- Check platform handlers
- Verify context passing
- Audit response generation

### Performance Problems

- Balance platform weights
- Implement caching
- Optimize database queries
- Use connection pooling

## Scaling Strategies

### Horizontal Scaling

- Separate workers per platform
- Load balancing
- Queue distribution
- Database replication

### Community Growth

- Gradual platform addition
- Community moderators
- Automated onboarding
- Cross-platform events

## Future Expansion

Ready to add:

- Slack integration
- Reddit participation
- Discord Stage events
- Instagram integration
- Custom platforms

## Success Metrics

Track these KPIs:

1. Cross-platform user retention
2. Conversation continuity
3. Platform-specific engagement
4. Community growth rate
5. Response quality consistency

---

**💡 Remember**: The magic of multi-platform Eliza is being the same helpful, curious personality everywhere while respecting what makes each platform unique. One character, many conversations, endless connections!
