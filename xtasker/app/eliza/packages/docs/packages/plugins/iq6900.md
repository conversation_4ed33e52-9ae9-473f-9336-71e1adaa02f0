# Code In Plugin For <PERSON>

## Description

Through IQ6900's new inscription standard "Code-In", powerful inscription functionality is provided to <PERSON>.
Engrave <PERSON> on the blockchain forever.
All your Character JSON files are input onto the blockchain without compression.
Everyone can read your agent from blocks forever.

## inscription

- **Code-in your eliza**: Go to the site and engrave your character file on-chain. https://elizacodein.com/

## Onchain git

- **Commit your update**:
  Update your files anytime.
  On our site, your files are managed in a format similar to GitHub,
  and our plugin automatically loads your latest agent file.

## Let's get started

- **Edit your .env**: write down IQ_WALLET_ADDRESS to your wallet address that you used on website.
  To be sure, right after inscription, wait about 5 minutes and just type pmpn start. You are now all set.

You have engraved an eternal record.
Imagine, someone could develop your agent 200 years from now.

Many things will be updated.

Learn more: https://iq6900.gitbook.io/iq6900/eliza-code-in
