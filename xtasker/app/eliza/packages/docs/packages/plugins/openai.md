# @elizaos/plugin-openai

A plugin for OpenAI integration, providing automated text generation capabilities.

## Overview

This plugin provides functionality to:

- Generate text using OpenAI's GPT models.
- Customize prompts for context-aware content generation.

## Installation

```bash
npm install @elizaos/plugin-openai
```

## Configuration

The plugin requires the following environment variable:

```env
OPENAI_API_KEY=your_openai_api_key
```

## Usage

Import and register the plugin in your Eliza configuration:

```typescript
import { openaiPlugin } from '@elizaos/plugin-openai';

export default {
  plugins: [openaiPlugin],
  // ... other configuration
};
```

### Generating Text

```typescript
const result = await generateTextAction.handler(runtime, message, state);
console.log(result.text); // Output generated by OpenAI
```

## Development

### Building

```bash
npm run build
```

### Testing

```bash
npm run test
```

## License

This plugin is part of the Eliza project. See the main project repository for license information.
