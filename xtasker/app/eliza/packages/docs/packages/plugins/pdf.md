# @elizaos/plugin-pdf

Core Node.js plugin for Eliza OS that provides essential services and actions for file operations.

## Overview

The Node plugin serves as a foundational component of Eliza OS, bridging core Node.js capabilities with the Eliza ecosystem. It provides crucial services for file operations, media processing, speech synthesis, and cloud integrations, enabling both local and cloud-based functionality for Eliza agents.

## Features

- **PDF Processing**: PDF text extraction and parsing

// ... existing code ...

## Services

### PdfService

Extracts and processes text content from PDF files.

// ... existing code ...

## License

This plugin is part of the Eliza project. See the main project repository for license information.
