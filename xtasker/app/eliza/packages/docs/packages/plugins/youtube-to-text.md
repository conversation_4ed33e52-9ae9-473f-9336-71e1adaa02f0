# `@elizaos/plugin-youtube-to-text`

## Description

The Youtube to text Plugin allows users to retrieve a text output of a Youtube video.

## Features

- Converts YouTube video links into text format using OpenAI’s Whisper model
- Leverages Whisper’s advanced AI for precise and reliable transcription
- Extracts and saves the video's audio as a WAV and MP3 file under the agent/data folder

## Installation

```bash
bun install @elizaos/plugin-youtube-to-text
```

## Development

1. Clone the repository
2. Install dependencies:

```bash
bun install
```

3. Build the plugin:

```bash
bun run build
```

4. Run tests:

```bash
bun test
```
