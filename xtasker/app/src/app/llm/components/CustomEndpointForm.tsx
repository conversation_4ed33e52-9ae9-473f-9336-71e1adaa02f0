import { useState, useEffect } from 'react';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Textarea } from '../../../components/ui/textarea';
import { Switch } from '../../../components/ui/switch';
import { Separator } from '../../../components/ui/separator';
import { LlmProviderFactory } from '../factory';
import type { LlmConfiguration } from 'wasp/entities';

interface CustomEndpointFormProps {
  provider: string;
  initialConfig?: LlmConfiguration | null;
  onSave: (config: Partial<LlmConfiguration>) => Promise<void>;
  onCancel: () => void;
}

export function CustomEndpointForm({ provider, initialConfig, onSave, onCancel }: CustomEndpointFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    provider: provider,
    model: '',
    baseUrl: '',
    apiKey: '',
    temperature: 0.7,
    maxTokens: 2000,
    topP: 1.0,
    frequencyPenalty: 0.0,
    presencePenalty: 0.0,
    isDefault: false,
    isActive: true,
    customHeaders: '',
  });

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (initialConfig) {
      setFormData({
        name: initialConfig.name,
        provider: initialConfig.provider,
        model: initialConfig.model,
        baseUrl: initialConfig.baseUrl || '',
        apiKey: initialConfig.apiKey || '',
        temperature: initialConfig.temperature || 0.7,
        maxTokens: initialConfig.maxTokens || 2000,
        topP: initialConfig.topP || 1.0,
        frequencyPenalty: initialConfig.frequencyPenalty || 0.0,
        presencePenalty: initialConfig.presencePenalty || 0.0,
        isDefault: initialConfig.isDefault,
        isActive: initialConfig.isActive,
        customHeaders: initialConfig.customHeaders || '',
      });
    } else {
      // Set defaults based on provider
      const providerInstance = LlmProviderFactory.getProvider(provider as any);
      const defaults = providerInstance.getDefaultConfig();
      setFormData(prev => ({
        ...prev,
        provider,
        ...defaults,
        customHeaders: typeof defaults.customHeaders === 'object'
          ? JSON.stringify(defaults.customHeaders)
          : (defaults.customHeaders || ''),
        name: `${providerInstance.name} Configuration`,
      }));
    }
  }, [provider, initialConfig]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const configToSave = {
        ...formData,
        ...(initialConfig && { id: initialConfig.id }),
      };

      await onSave(configToSave);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const getModelPlaceholder = () => {
    switch (provider) {
      case 'openai':
        return 'gpt-3.5-turbo, gpt-4, gpt-4-turbo';
      case 'anthropic':
        return 'claude-3-sonnet-20240229, claude-3-opus-20240229';
      case 'local':
        return 'llama2, codellama, mistral';
      default:
        return 'Enter model name';
    }
  };

  const getBaseUrlPlaceholder = () => {
    switch (provider) {
      case 'openai':
        return 'https://api.openai.com/v1';
      case 'anthropic':
        return 'https://api.anthropic.com';
      case 'local':
        return 'http://localhost:11434/v1';
      default:
        return 'https://your-api-endpoint.com/v1';
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Configuration */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Basic Configuration</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="name">Configuration Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="My OpenAI Config"
              required
            />
          </div>
          
          <div>
            <Label htmlFor="model">Model</Label>
            <Input
              id="model"
              value={formData.model}
              onChange={(e) => handleInputChange('model', e.target.value)}
              placeholder={getModelPlaceholder()}
              required
            />
          </div>
        </div>

        <div>
          <Label htmlFor="baseUrl">Base URL {provider === 'custom' || provider === 'local' ? '(Required)' : '(Optional)'}</Label>
          <Input
            id="baseUrl"
            value={formData.baseUrl}
            onChange={(e) => handleInputChange('baseUrl', e.target.value)}
            placeholder={getBaseUrlPlaceholder()}
            required={provider === 'custom' || provider === 'local'}
          />
        </div>

        <div>
          <Label htmlFor="apiKey">API Key {provider === 'local' ? '(Optional)' : '(Required)'}</Label>
          <Input
            id="apiKey"
            type="password"
            value={formData.apiKey}
            onChange={(e) => handleInputChange('apiKey', e.target.value)}
            placeholder="sk-..."
            required={provider !== 'local'}
          />
        </div>
      </div>

      <Separator />

      {/* Model Parameters */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Model Parameters</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="temperature">Temperature (0-2)</Label>
            <Input
              id="temperature"
              type="number"
              min="0"
              max="2"
              step="0.1"
              value={formData.temperature}
              onChange={(e) => handleInputChange('temperature', parseFloat(e.target.value))}
            />
          </div>
          
          <div>
            <Label htmlFor="maxTokens">Max Tokens</Label>
            <Input
              id="maxTokens"
              type="number"
              min="1"
              max="32000"
              value={formData.maxTokens}
              onChange={(e) => handleInputChange('maxTokens', parseInt(e.target.value))}
            />
          </div>
        </div>

        {(provider === 'openai' || provider === 'custom') && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="topP">Top P (0-1)</Label>
              <Input
                id="topP"
                type="number"
                min="0"
                max="1"
                step="0.1"
                value={formData.topP}
                onChange={(e) => handleInputChange('topP', parseFloat(e.target.value))}
              />
            </div>
            
            <div>
              <Label htmlFor="frequencyPenalty">Frequency Penalty (-2 to 2)</Label>
              <Input
                id="frequencyPenalty"
                type="number"
                min="-2"
                max="2"
                step="0.1"
                value={formData.frequencyPenalty}
                onChange={(e) => handleInputChange('frequencyPenalty', parseFloat(e.target.value))}
              />
            </div>
            
            <div>
              <Label htmlFor="presencePenalty">Presence Penalty (-2 to 2)</Label>
              <Input
                id="presencePenalty"
                type="number"
                min="-2"
                max="2"
                step="0.1"
                value={formData.presencePenalty}
                onChange={(e) => handleInputChange('presencePenalty', parseFloat(e.target.value))}
              />
            </div>
          </div>
        )}
      </div>

      <Separator />

      {/* Advanced Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Advanced Settings</h3>
        
        <div>
          <Label htmlFor="customHeaders">Custom Headers (JSON format)</Label>
          <Textarea
            id="customHeaders"
            value={formData.customHeaders}
            onChange={(e) => handleInputChange('customHeaders', e.target.value)}
            placeholder='{"Authorization": "Bearer token", "Custom-Header": "value"}'
            rows={3}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label>Set as Default</Label>
            <p className="text-sm text-muted-foreground">
              Use this configuration as the default for new tasks
            </p>
          </div>
          <Switch
            checked={formData.isDefault}
            onCheckedChange={(checked) => handleInputChange('isDefault', checked)}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label>Active</Label>
            <p className="text-sm text-muted-foreground">
              Enable this configuration for use
            </p>
          </div>
          <Switch
            checked={formData.isActive}
            onCheckedChange={(checked) => handleInputChange('isActive', checked)}
          />
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? 'Saving...' : initialConfig ? 'Update' : 'Save'}
        </Button>
      </div>
    </form>
  );
}