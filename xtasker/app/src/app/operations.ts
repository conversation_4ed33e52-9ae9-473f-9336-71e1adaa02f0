import type { PrismaPromise } from '@prisma/client';
import type { LlmResponse, Task, User, LlmConfiguration } from 'wasp/entities';
import { HttpError, prisma } from 'wasp/server';
import type {
  CreateTask,
  DeleteTask,
  GenerateLlmResponse,
  GetAllTasksByUser,
  GetLlmResponses,
  UpdateTask,
} from 'wasp/server/operations';
import * as z from 'zod';
import { SubscriptionStatus } from '../payment/plans';
import { ensureArgsSchemaOrThrowHttpError } from '../server/validation';
import { GeneratedSchedule, TaskPriority } from './schedule';
import { LlmProviderFactory } from './llm/factory';
import type { LlmConfig } from './llm/types';

//#region Actions
const generateLlmResponseInputSchema = z.object({
  hours: z.number(),
});

type GenerateLlmResponseInput = z.infer<typeof generateLlmResponseInputSchema>;

export const generateLlmResponse: GenerateLlmResponse<GenerateLlmResponseInput, GeneratedSchedule> = async (
  rawArgs,
  context
) => {
  if (!context.user) {
    throw new HttpError(401, 'Only authenticated users are allowed to perform this operation');
  }

  const { hours } = ensureArgsSchemaOrThrowHttpError(generateLlmResponseInputSchema, rawArgs);
  const tasks = await context.entities.Task.findMany({
    where: {
      user: {
        id: context.user.id,
      },
    },
  });

  console.log('Calling LLM API');
  const llmConfig = await getLlmConfig(context.user.id);
  const generatedSchedule = await generateScheduleWithLlm(tasks, hours, llmConfig);
  if (generatedSchedule === null) {
    throw new HttpError(500, 'Encountered a problem in communication with LLM');
  }

  const createResponse = context.entities.LlmResponse.create({
    data: {
      user: { connect: { id: context.user.id } },
      content: JSON.stringify(generatedSchedule),
    },
  });

  const transactions: PrismaPromise<LlmResponse | User>[] = [createResponse];

  // We decrement the credits for users without an active subscription
  // after using up tokens to get a daily plan from Chat GPT.
  //
  // This way, users don't feel cheated if something goes wrong.
  // On the flipside, users can theoretically abuse this and spend more
  // credits than they have, but the damage should be pretty limited.
  //
  // Think about which option you prefer for your app and edit the code accordingly.
  if (!isUserSubscribed(context.user)) {
    if (context.user.credits > 0) {
      const decrementCredit = context.entities.User.update({
        where: { id: context.user.id },
        data: {
          credits: {
            decrement: 1,
          },
        },
      });
      transactions.push(decrementCredit);
    } else {
      throw new HttpError(402, 'User has not paid or is out of credits');
    }
  }

  console.log('Decrementing credits and saving response');
  await prisma.$transaction(transactions);

  return generatedSchedule;
};

function isUserSubscribed(user: User) {
  return (
    user.subscriptionStatus === SubscriptionStatus.Active ||
    user.subscriptionStatus === SubscriptionStatus.CancelAtPeriodEnd
  );
}

const createTaskInputSchema = z.object({
  description: z.string().nonempty(),
});

type CreateTaskInput = z.infer<typeof createTaskInputSchema>;

export const createTask: CreateTask<CreateTaskInput, Task> = async (rawArgs, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  const { description } = ensureArgsSchemaOrThrowHttpError(createTaskInputSchema, rawArgs);

  const task = await context.entities.Task.create({
    data: {
      description,
      user: { connect: { id: context.user.id } },
    },
  });

  return task;
};

const updateTaskInputSchema = z.object({
  id: z.string().nonempty(),
  isDone: z.boolean().optional(),
  time: z.string().optional(),
});

type UpdateTaskInput = z.infer<typeof updateTaskInputSchema>;

export const updateTask: UpdateTask<UpdateTaskInput, Task> = async (rawArgs, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  const { id, isDone, time } = ensureArgsSchemaOrThrowHttpError(updateTaskInputSchema, rawArgs);

  const task = await context.entities.Task.update({
    where: {
      id,
      user: {
        id: context.user.id,
      },
    },
    data: {
      isDone,
      time,
    },
  });

  return task;
};

const deleteTaskInputSchema = z.object({
  id: z.string().nonempty(),
});

type DeleteTaskInput = z.infer<typeof deleteTaskInputSchema>;

export const deleteTask: DeleteTask<DeleteTaskInput, Task> = async (rawArgs, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  const { id } = ensureArgsSchemaOrThrowHttpError(deleteTaskInputSchema, rawArgs);

  const task = await context.entities.Task.delete({
    where: {
      id,
      user: {
        id: context.user.id,
      },
    },
  });

  return task;
};
//#endregion

//#region Queries
export const getLlmResponses: GetLlmResponses<void, LlmResponse[]> = async (_args, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }
  return context.entities.LlmResponse.findMany({
    where: {
      user: {
        id: context.user.id,
      },
    },
  });
};

export const getAllTasksByUser: GetAllTasksByUser<void, Task[]> = async (_args, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }
  return context.entities.Task.findMany({
    where: {
      user: {
        id: context.user.id,
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });
};
//#endregion

// Helper function to get user's default LLM configuration
async function getUserDefaultLlmConfig(userId: string, context: any): Promise<LlmConfig> {
  const defaultConfig = await context.entities.LlmConfiguration.findFirst({
    where: {
      userId,
      isDefault: true,
      isActive: true,
    },
  });

  if (defaultConfig) {
    return {
      provider: defaultConfig.provider,
      model: defaultConfig.model,
      baseUrl: defaultConfig.baseUrl || undefined,
      apiKey: defaultConfig.apiKey || undefined,
      temperature: defaultConfig.temperature || 0.7,
      maxTokens: defaultConfig.maxTokens || 2000,
      topP: defaultConfig.topP || 1.0,
      frequencyPenalty: defaultConfig.frequencyPenalty || 0.0,
      presencePenalty: defaultConfig.presencePenalty || 0.0,
      customHeaders: defaultConfig.customHeaders ? JSON.parse(defaultConfig.customHeaders) : undefined,
    };
  }

  // Fallback to environment-based OpenAI config
  if (process.env.OPENAI_API_KEY) {
    return {
      provider: 'openai',
      model: 'gpt-3.5-turbo',
      apiKey: process.env.OPENAI_API_KEY,
      temperature: 0.7,
      maxTokens: 2000,
    };
  }

  throw new HttpError(500, 'No LLM configuration found. Please configure an LLM provider.');
}

async function generateScheduleWithLlm(tasks: Task[], hours: number, config: LlmConfig): Promise<GeneratedSchedule | null> {
  const parsedTasks = tasks.map(({ description, time }) => ({
    description,
    time,
  }));

  const provider = LlmProviderFactory.getProvider(config.provider);

  const systemPrompt = 'you are an expert daily planner. you will be given a list of main tasks and an estimated time to complete each task. You will also receive the total amount of hours to be worked that day. Your job is to return a detailed plan of how to achieve those tasks by breaking each task down into at least 3 subtasks each. MAKE SURE TO ALWAYS CREATE AT LEAST 3 SUBTASKS FOR EACH MAIN TASK PROVIDED BY THE USER! YOU WILL BE REWARDED IF YOU DO.';

  const userPrompt = `I will work ${hours} hours today. Here are the tasks I have to complete: ${JSON.stringify(
    parsedTasks
  )}. Please help me plan my day by breaking the tasks down into actionable subtasks with time and priority status.

Please respond with a JSON object in the following format:
{
  "tasks": [
    {
      "mainTaskName": "Task name",
      "priority": "high|medium|low",
      "subtasks": [
        {
          "description": "Subtask description",
          "time": 30,
          "priority": "high|medium|low"
        }
      ]
    }
  ]
}`;

  const fullPrompt = `${systemPrompt}\n\n${userPrompt}`;

  const response = await provider.generateResponse(fullPrompt, config);
  try {
    const result = JSON.parse(response.content);
    return result;
  } catch (error) {
    console.error('Failed to parse LLM response:', error);
    return null;
  }
}
